import { publicProcedureWithLogging, createTRPCRouter } from './trpc';
import { identityRouter } from './routers/identity';
import { financeRouter } from './routers/finance';
import { calcRouter } from './routers/calc';

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
	// Health check for the entire API
	healthCheck: publicProcedureWithLogging.query(async () => {
		return {
			status: 'healthy',
			timestamp: new Date().toISOString(),
			version: '1.0.0',
			services: {
				finance: 'available',
				identity: 'available',
				calc: 'available'
			},
			uptime: process.uptime()
		};
	}),

	// Service routers
	finance: financeRouter,
	identity: identityRouter,
	calc: calcRouter
});

// Export type definition of API
export type AppRouter = typeof appRouter;