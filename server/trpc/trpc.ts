import { TRPCError, initTRPC } from '@trpc/server';
import { z } from 'zod';

// Create context for tRPC
export interface Context {
	// Add user authentication context here later
	userId?: string;
	// Add request/response objects if needed
	req?: any;
	res?: any;
}

// Initialize tRPC
const t = initTRPC.context<Context>().create({
	errorFormatter({ shape, error }) {
		return {
			...shape,
			data: {
				...shape.data,
				zodError: error.cause instanceof z.ZodError ? error.cause.flatten() : null
			}
		};
	}
});

// Export reusable router and procedure helpers
export const createTRPCRouter = t.router;
export const publicProcedure = t.procedure;

// Protected procedure (requires authentication)
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
	if (!ctx.userId) {
		throw new TRPCError({
			code: 'UNAUTHORIZED',
			message: 'You must be logged in to access this resource'
		});
	}
	return next({
		ctx: {
			...ctx,
			userId: ctx.userId
		}
	});
});

// Middleware for logging
export const loggerMiddleware = t.middleware(async ({ path, type, next }) => {
	const start = Date.now();
	const result = await next();
	const durationMs = Date.now() - start;

	console.log(`[tRPC] ${type} ${path} - ${durationMs}ms`);

	return result;
});

// Public procedure with logging
export const publicProcedureWithLogging = publicProcedure.use(loggerMiddleware);

// Protected procedure with logging
export const protectedProcedureWithLogging = protectedProcedure.use(loggerMiddleware);