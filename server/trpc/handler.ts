import { fetchRe<PERSON>Handler } from '@trpc/server/adapters/fetch';
import type { Context } from './trpc';
import { appRouter } from './root';

// Create context from request
function createContext(req: Request): Context {
	// Extract user ID from Authorization header (JWT token)
	const authHeader = req.headers.get('authorization');
	let userId: string | undefined;

	if (authHeader?.startsWith('Bearer ')) {
		const token = authHeader.substring(7);
		// TODO: Implement proper JWT token validation
		// For now, just extract a mock user ID
		if (token && token !== 'null' && token !== 'undefined') {
			userId = 'user_123'; // Mock user ID
		}
	}

	return {
		userId,
		req
	};
}

// tRPC request handler
export const trpcHandler = (request: Request) => {
	return fetchRequestHandler({
		endpoint: '/api/trpc',
		req: request,
		router: appRouter,
		createContext: () => createContext(request),
		onError: ({ error, path, input }) => {
			console.error(`[tRPC Error] ${path}:`, error);

			// Log input for debugging (be careful with sensitive data)
			if (process.env.NODE_ENV === 'development') {
				console.error('Input:', input);
			}
		}
	});
};

// Export the router type for client-side usage
export type { AppRouter } from './root';