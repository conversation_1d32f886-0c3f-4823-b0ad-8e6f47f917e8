// SERVER tsconfig (root dir is inferred)
{
	"extends": "../tsconfig.json",
	"compilerOptions": {
		"lib": ["ESNext"],
		"types": ["bun-types", "@types/node"],

		"noEmit": true, // Server should NOT emit files, <PERSON><PERSON> handles transpilation directly.
		"allowJs": true,

		"baseUrl": "..", // Base URL for the project root
		"paths": {
			"@/*": ["./*"]
		}
	},
	"include": ["**/*.ts"],
	"exclude": ["node_modules", "../client", "../tests"]
}