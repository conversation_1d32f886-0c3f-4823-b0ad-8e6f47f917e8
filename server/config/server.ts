// server/config/server.ts - Production server
import { validateEnv, ENV } from '@/shared/config/env';
import indexHtml from '../../client/public/index.html';

// Validate environment configuration
validateEnv();

const env_client = ENV;

console.log(`🚀 Starting Bun production server...`);
console.log(`📦 Environment: ${env_client.ENV}`);
console.log(`🔗 Service URLs:`);
console.log(`   - Identity (Auth): ${env_client.IDP_SERVICE_URL}`);
console.log(`   - Finance: ${env_client.FINANCE_SERVICE_URL}`);
console.log(`   - Calculation: ${env_client.CALC_SERVICE_URL}`);

const server = Bun.serve({
	port: env_client.PORT,

	// Use Bun's fullstack server with HTML imports (production mode)
	routes: {
		'/': indexHtml // This automatically bundles and serves the HTML with all assets
	},

	// Production mode - no HMR, optimized builds
	development: false,

	async fetch(request: Request): Promise<Response> {
		const url = new URL(request.url);
		const pathname = url.pathname;

		// API Routing - Proxy to appropriate microservices
		if (pathname.startsWith('/api/')) {
			try {
				let targetUrl: string;

				// Route API requests to appropriate services
				if (pathname.startsWith('/api/auth/')) {
					targetUrl = env_client.IDP_SERVICE_URL;
				} else if (pathname.startsWith('/api/calc/')) {
					targetUrl = env_client.CALC_SERVICE_URL;
				} else if (pathname.startsWith('/api/finance/')) {
					targetUrl = env_client.FINANCE_SERVICE_URL;
				} else {
					// Default to Finance service for unmatched API routes
					targetUrl = env_client.FINANCE_SERVICE_URL;
				}

				// Remove '/api' prefix and forward to the service
				const servicePath = pathname.replace('/api', '');
				const serviceUrl = `${targetUrl}${servicePath}${url.search}`;

				// Forward the request to the appropriate service
				const response = await fetch(serviceUrl, {
					method: request.method,
					headers: {
						...Object.fromEntries(request.headers.entries()),
						'X-Forwarded-For': 'production-server'
					},
					body: request.method !== 'GET' && request.method !== 'HEAD' ? request.body : undefined
				});

				return response;
			} catch (error) {
				console.error(`❌ Proxy error for ${pathname}:`, error);
				return new Response(
					JSON.stringify({
						error: 'Service Unavailable',
						message: `Failed to reach backend service for ${pathname}`,
						details: error instanceof Error ? error.message : 'Unknown error'
					}),
					{
						status: 503,
						headers: { 'Content-Type': 'application/json' }
					}
				);
			}
		}

		// Let Bun handle all other requests (static files, etc.)
		return new Response('Not Found', { status: 404 });
	},

	error(error: Error) {
		console.error('💥 [Server Error]', error);
		return new Response(
			JSON.stringify({
				error: 'Internal Server Error',
				message: error.message
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
});

console.log(`✅ Production server running at http://localhost:${server.port}`);
console.log(`🔄 API requests will be proxied to backend services`);
console.log(`📦 Static assets served with optimized builds and caching`);
console.log(`🛑 Press Ctrl+C to stop the server\n`);