import { generateTestUserForRegistration, TEST_CONFIG } from '@/tests/config/test.config';
import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';

describe('Identity Service - Direct API Registration', () => {
	let createdUsers: string[] = []; // Track users for cleanup

	beforeAll(async () => {
		console.log('🔍 Checking Identity service readiness...');
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}
		console.log('✅ Identity service is ready for direct API registration tests');
	});

	afterAll(async () => {
		// Cleanup all created users
		for (const email of createdUsers) {
			try {
				await TRPCTestHelper.cleanupTestUser(email);
			} catch (error) {
				console.warn(`⚠️ Failed to cleanup user: ${email}`, error);
			}
		}
		console.log('🧹 Test user cleanup completed');
	});

	describe('User Registration', () => {
		it('should successfully register a new user via Direct API', async () => {
			const userPrefix = 'reg-success';

			const { result, responseTime } = await TRPCTestHelper.measureResponseTime(async () => {
				return await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);
			});

			// Track for cleanup
			if (result.user?.email) {
				createdUsers.push(result.user.email);
			}

			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.tokens?.accessToken).toBeDefined();
			expect(result.tokens?.refreshToken).toBeDefined();
			expect(result.user).toBeDefined();
			expect(result.user?.email).toContain(userPrefix);

			// Check performance
			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.REGISTRATION_MAX_TIME);
			console.log(`✅ Registration completed in ${responseTime}ms`);
		});

		it('should fail registration with duplicate email via Direct API', async () => {
			const userPrefix = 'reg-duplicate';

			// First registration should succeed
			const firstResult = await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);
			expect(firstResult.success).toBe(true);

			if (firstResult.user?.email) {
				createdUsers.push(firstResult.user.email);
			}

			// Second registration with same email should fail
			// Since createTestUserInIdentityService generates unique emails with timestamps,
			// we need to test duplicate email differently - by using direct API call
			try {
				const userData = generateTestUserForRegistration('duplicate-test');
				userData.email = firstResult.user?.email || '<EMAIL>'; // Use the same email

				const request = TRPCTestHelper.getIdentityServiceRequest();
				await request
					.post('/auth/register')
					.send(userData)
					.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				// The error should indicate duplicate email or conflict
				console.log('✅ Duplicate email correctly rejected');
			}
		});

		it('should fail registration with weak password via Direct API', async () => {
			const weakPasswords = ['123456', 'password', 'Password', 'Password123', 'Pass@1'];

			for (const weakPassword of weakPasswords) {
				try {
					// This should fail due to weak password
					const userPrefix = `weak-pass-${Date.now()}`;
					const userData = generateTestUserForRegistration(userPrefix);

					const request = TRPCTestHelper.getIdentityServiceRequest();
					await request
						.post('/auth/register')
						.send({
							...userData,
							password: weakPassword
						})
						.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

					// Should not reach here
					expect(true).toBe(false);
				} catch (error: any) {
					expect(error).toBeDefined();
					console.log(`✅ Weak password "${weakPassword}" correctly rejected`);
				}
			}
		});

		it('should fail registration with missing required fields via Direct API', async () => {
			const requiredFields = ['email', 'password', 'firstName', 'lastName'];

			for (const missingField of requiredFields) {
				try {
					const userPrefix = `missing-${missingField}`;
					const userData = generateTestUserForRegistration(userPrefix);

					// Remove the required field
					delete (userData as any)[missingField];

					const request = TRPCTestHelper.getIdentityServiceRequest();
					await request
						.post('/auth/register')
						.send(userData)
						.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

					// Should not reach here
					expect(true).toBe(false);
				} catch (error: any) {
					expect(error).toBeDefined();
					console.log(`✅ Missing required field "${missingField}" correctly rejected`);
				}
			}
		});
	});

	describe('Registration Validation', () => {
		it('should validate email domain restrictions if any', async () => {
			try {
				const userPrefix = 'domain-test';
				const userData = generateTestUserForRegistration(userPrefix);

				// Use a potentially restricted domain
				userData.email = `test-${Date.now()}@restricted-domain.com`;

				const request = TRPCTestHelper.getIdentityServiceRequest();
				await request
					.post('/auth/register')
					.send(userData)
					.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

				// If it succeeds, that's fine - no domain restrictions
				console.log('✅ No domain restrictions enforced');
			} catch (error: any) {
				// If it fails, check if it's due to domain restrictions
				expect(error).toBeDefined();
				console.log('✅ Domain restrictions enforced:', error.message);
			}
		});

		it('should handle special characters in names correctly', async () => {
			const userPrefix = 'special-chars';
			const userData = generateTestUserForRegistration(userPrefix);

			// Test with special characters in names
			userData.firstName = 'José-María';
			userData.lastName = "O'Connor-Smith";

			try {
				const request = TRPCTestHelper.getIdentityServiceRequest();
				const response = await request
					.post('/auth/register')
					.send(userData)
					.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

				if (response.body?.data?.tokenPayload?.email) {
					createdUsers.push(response.body.data.tokenPayload.email);
				}

				expect(response.status).toBe(201);
				console.log('✅ Special characters in names handled correctly');
			} catch (error: any) {
				// Some systems might not allow special characters
				expect(error).toBeDefined();
				console.log('✅ Special characters in names properly validated');
			}
		});
	});

	describe('Registration Performance and Reliability', () => {
		it('should handle multiple concurrent registrations', async () => {
			const concurrentRegistrations = Array(3)
				.fill(null)
				.map((_, index) => 
					TRPCTestHelper.createTestUserInIdentityService(`concurrent-${index}`, false)
				);

			const results = await Promise.allSettled(concurrentRegistrations);

			// Track successful registrations for cleanup
			results.forEach(result => {
				if (result.status === 'fulfilled' && result.value.user?.email) {
					createdUsers.push(result.value.user.email);
				}
			});

			// At least some should succeed
			const successfulResults = results.filter(r => r.status === 'fulfilled');
			expect(successfulResults.length).toBeGreaterThan(0);
			console.log(`✅ Handled ${successfulResults.length}/${results.length} concurrent registrations`);
		});

		it('should maintain consistent response structure', async () => {
			const userPrefix = 'structure-test';
			const result = await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);

			if (result.user?.email) {
				createdUsers.push(result.user.email);
			}

			// Verify response structure
			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('tokens');
			expect(result).toHaveProperty('user');

			if (result.tokens) {
				expect(result.tokens).toHaveProperty('accessToken');
				expect(result.tokens).toHaveProperty('refreshToken');
			}

			if (result.user) {
				expect(result.user).toHaveProperty('email');
				expect(result.user).toHaveProperty('id');
			}

			console.log('✅ Response structure is consistent');
		});
	});
});