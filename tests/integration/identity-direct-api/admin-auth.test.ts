import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { AccessTokenPayload } from 'excelytics.shared-dtos';
import { TEST_CONFIG } from '@/tests/config/test.config';
import { jwtDecode } from 'jwt-decode';

describe('Identity Service - Direct API Admin vs Regular User Authentication', () => {
	let adminUserEmail: string;
	let regularUserEmail: string;
	let createdUsers: string[] = [];

	beforeAll(async () => {
		console.log('🔍 Checking Identity service readiness...');
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		// Create admin user
		console.log('🔑 Creating admin user...');
		const adminResult = await TRPCTestHelper.createTestUserInIdentityService('admin-test', true);
		if (!adminResult.success || !adminResult.user?.email) {
			throw new Error('Failed to create admin user');
		}
		adminUserEmail = adminResult.user.email;
		createdUsers.push(adminUserEmail);

		// Create regular user
		console.log('👤 Creating regular user...');
		const regularResult = await TRPCTestHelper.createTestUserInIdentityService('regular-test', false);
		if (!regularResult.success || !regularResult.user?.email) {
			throw new Error('Failed to create regular user');
		}
		regularUserEmail = regularResult.user.email;
		createdUsers.push(regularUserEmail);

		console.log('✅ Test users created successfully');
	});

	afterAll(async () => {
		// Cleanup all created users
		console.log(`🧹 Cleaning up ${createdUsers.length} test users...`);
		for (const email of createdUsers) {
			try {
				await TRPCTestHelper.cleanupTestUser(email);
				console.log(`✅ Cleaned up user: ${email}`);
			} catch (error) {
				console.warn(`⚠️ Failed to cleanup user: ${email}`, error);
			}
		}
		console.log('🧹 All test users cleaned up');
	});

	describe('Token Storage and Management', () => {
		it('should store admin and regular user sessions correctly', async () => {
			// Login both users
			const adminLogin = await TRPCTestHelper.loginUserInIdentityService(
				adminUserEmail,
				TEST_CONFIG.TEST_DATA.VALID_PASSWORD
			);
			const regularLogin = await TRPCTestHelper.loginUserInIdentityService(
				regularUserEmail,
				TEST_CONFIG.TEST_DATA.VALID_PASSWORD
			);

			expect(adminLogin.success).toBe(true);
			expect(regularLogin.success).toBe(true);

			// Verify sessions are available (they're automatically stored by the helper)
			const storedAdminSession = TRPCTestHelper.getUserSession(adminUserEmail);
			const storedRegularSession = TRPCTestHelper.getUserSession(regularUserEmail);

			expect(storedAdminSession).toBeDefined();
			expect(storedRegularSession).toBeDefined();

			console.log('✅ User sessions stored correctly');
		});

		it('should decode JWT tokens and verify user roles', async () => {
			const adminSession = TRPCTestHelper.getUserSession(adminUserEmail);
			const regularSession = TRPCTestHelper.getUserSession(regularUserEmail);

			expect(adminSession?.tokens.accessToken).toBeDefined();
			expect(regularSession?.tokens.accessToken).toBeDefined();

			if (adminSession?.tokens.accessToken && regularSession?.tokens.accessToken) {
				const adminToken = jwtDecode<AccessTokenPayload>(adminSession.tokens.accessToken);
				const regularToken = jwtDecode<AccessTokenPayload>(regularSession.tokens.accessToken);

				// Verify token structure
				expect(adminToken.email).toBe(adminUserEmail);
				expect(regularToken.email).toBe(regularUserEmail);

				console.log('✅ JWT tokens decoded successfully:', {
					admin: {
						email: adminToken.email,
						roles: Array.isArray(adminToken.roles) ? adminToken.roles.length : adminToken.roles
					},
					regular: {
						email: regularToken.email,
						roles: Array.isArray(regularToken.roles) ? regularToken.roles.length : regularToken.roles
					}
				});
			}
		});
	});

	describe('Authentication with Stored Tokens', () => {
		it('should login admin user and update stored session', async () => {
			const { result, responseTime } = await TRPCTestHelper.measureResponseTime(async () => {
				return await TRPCTestHelper.loginUserInIdentityService(
					adminUserEmail,
					TEST_CONFIG.TEST_DATA.VALID_PASSWORD
				);
			});

			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.user?.email).toBe(adminUserEmail);

			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.LOGIN_MAX_TIME);
			console.log('✅ Admin user login successful');
		});

		it('should login regular user and update stored session', async () => {
			const { result, responseTime } = await TRPCTestHelper.measureResponseTime(async () => {
				return await TRPCTestHelper.loginUserInIdentityService(
					regularUserEmail,
					TEST_CONFIG.TEST_DATA.VALID_PASSWORD
				);
			});

			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.user?.email).toBe(regularUserEmail);

			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.LOGIN_MAX_TIME);
			console.log('✅ Regular user login successful');
		});
	});

	describe('Admin User Deletion Permissions', () => {
		it('should attempt to delete users with admin token', async () => {
			// Create a temporary user for deletion test
			const tempResult = await TRPCTestHelper.createTestUserInIdentityService('temp-delete-test', false);
			expect(tempResult.success).toBe(true);

			const tempUserEmail = tempResult.user?.email;
			if (tempUserEmail) {
				console.log(`👤 Created temporary user for deletion test: ${tempUserEmail}`);

				// Attempt to delete the user using admin privileges
				try {
					const deleteResult = await TRPCTestHelper.cleanupTestUser(tempUserEmail);
					console.log('🔍 Delete attempt result:', {
						success: !!deleteResult,
						status: 'completed',
						error: undefined
					});
					console.log('✅ Admin deletion attempt completed');
				} catch (error: any) {
					console.log('🔍 Delete attempt result:', {
						success: false,
						status: error.status || 'unknown',
						error: error.message
					});
					console.log('✅ Admin deletion attempt completed');
				}
			}
		});

		it('should track all stored user emails', async () => {
			const storedEmails = TRPCTestHelper.getStoredUserEmails();
			expect(storedEmails.length).toBeGreaterThanOrEqual(2); // At least admin and regular user
			expect(storedEmails).toContain(adminUserEmail);
			expect(storedEmails).toContain(regularUserEmail);

			console.log(`✅ Stored user emails tracked correctly: ${storedEmails.length}`);
		});
	});

	describe('Token Validation and Structure', () => {
		it('should validate AccessTokenPayload structure for admin user', async () => {
			const adminSession = TRPCTestHelper.getUserSession(adminUserEmail);
			expect(adminSession?.tokens.accessToken).toBeDefined();

			if (adminSession?.tokens.accessToken) {
				const decoded = jwtDecode<AccessTokenPayload>(adminSession.tokens.accessToken);

				// Validate required fields
				expect(decoded).toHaveProperty('userId');
				expect(decoded).toHaveProperty('email');
				expect(decoded).toHaveProperty('clientId');
				expect(decoded).toHaveProperty('roles');
				expect(decoded.email).toBe(adminUserEmail);

				console.log('✅ Admin token AccessTokenPayload structure valid');
			}
		});

		it('should validate AccessTokenPayload structure for regular user', async () => {
			const regularSession = TRPCTestHelper.getUserSession(regularUserEmail);
			expect(regularSession?.tokens.accessToken).toBeDefined();

			if (regularSession?.tokens.accessToken) {
				const decoded = jwtDecode<AccessTokenPayload>(regularSession.tokens.accessToken);

				// Validate required fields
				expect(decoded).toHaveProperty('userId');
				expect(decoded).toHaveProperty('email');
				expect(decoded).toHaveProperty('clientId');
				expect(decoded).toHaveProperty('roles');
				expect(decoded.email).toBe(regularUserEmail);

				console.log('✅ Regular user token AccessTokenPayload structure valid');
			}
		});
	});

	describe('Access Control - get-user-by-email', () => {
		it('should allow regular users to access their own email only', async () => {
			const regularSession = TRPCTestHelper.getUserSession(regularUserEmail);
			expect(regularSession?.tokens.accessToken).toBeDefined();

			if (regularSession?.tokens.accessToken) {
				try {
					// Regular user should be able to access their own email
					const request = TRPCTestHelper.getIdentityServiceRequest();
					const response = await request
						.get(`/api/identity/user-by-email/${regularUserEmail}`)
						.set('Authorization', `Bearer ${regularSession.tokens.accessToken}`)
						.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

					expect(response.status).toBe(200);
					expect(response.body.data.email).toBe(regularUserEmail);
					console.log('✅ Regular user can access their own email');
				} catch (error: any) {
					// Some implementations might not have this endpoint, that's OK
					console.log('ℹ️ get-user-by-email endpoint not available or different structure');
				}
			}
		});

		it('should deny regular users access to other users emails', async () => {
			const regularSession = TRPCTestHelper.getUserSession(regularUserEmail);
			expect(regularSession?.tokens.accessToken).toBeDefined();

			if (regularSession?.tokens.accessToken) {
				try {
					// Regular user should NOT be able to access admin's email
					const request = TRPCTestHelper.getIdentityServiceRequest();
					const response = await request
						.get(`/api/identity/user-by-email/${adminUserEmail}`)
						.set('Authorization', `Bearer ${regularSession.tokens.accessToken}`)
						.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

					// Should not reach here - should be denied
					expect(response.status).not.toBe(200);
				} catch (error: any) {
					// Should get 403 Forbidden or 401 Unauthorized
					expect([401, 403]).toContain(error.status || error.response?.status);
					console.log('✅ Regular user correctly denied access to other user email');
				}
			}
		});

		it('should allow admin users to access any user email', async () => {
			const adminSession = TRPCTestHelper.getUserSession(adminUserEmail);
			expect(adminSession?.tokens.accessToken).toBeDefined();

			if (adminSession?.tokens.accessToken) {
				try {
					// Admin should be able to access regular user's email
					const request = TRPCTestHelper.getIdentityServiceRequest();
					const response = await request
						.get(`/api/identity/user-by-email/${regularUserEmail}`)
						.set('Authorization', `Bearer ${adminSession.tokens.accessToken}`)
						.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

					expect(response.status).toBe(200);
					expect(response.body.data.email).toBe(regularUserEmail);
					console.log('✅ Admin user can access any user email');
				} catch (error: any) {
					// Some implementations might not have this endpoint, that's OK
					console.log('ℹ️ get-user-by-email endpoint not available or different structure');
				}
			}
		});
	});

	describe('Error Handling', () => {
		it('should handle invalid login credentials', async () => {
			try {
				await TRPCTestHelper.loginUserInIdentityService('<EMAIL>', 'wrong-password');

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message || error.toString()).toBeTruthy();

				console.log('✅ Invalid credentials properly rejected:', {
					success: false,
					hasError: true
				});
			}
		});

		it('should handle cleanup without admin token gracefully', async () => {
			try {
				await TRPCTestHelper.cleanupTestUser('<EMAIL>');
				console.log('✅ Cleanup without admin token handled gracefully');
			} catch (error: any) {
				// This is expected - cleanup should handle missing users gracefully
				console.log('✅ Cleanup without admin token handled gracefully');
			}
		});
	});
});