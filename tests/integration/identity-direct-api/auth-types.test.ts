import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { AccessTokenPayload } from 'excelytics.shared-dtos';
import { TEST_CONFIG } from '@/tests/config/test.config';
import { jwtDecode } from 'jwt-decode';

describe('Identity Service - Direct API Authentication Types', () => {
	let testUserEmail: string;
	let testUserPassword: string;
	let createdUsers: string[] = [];

	beforeAll(async () => {
		console.log('🔍 Checking Identity service readiness...');
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		// Create a test user for authentication type validation
		const userPrefix = 'auth-types-test';
		const result = await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);

		if (!result.success || !result.user?.email) {
			throw new Error('Failed to create test user for auth type tests');
		}

		testUserEmail = result.user.email;
		testUserPassword = TEST_CONFIG.TEST_DATA.VALID_PASSWORD;
		createdUsers.push(testUserEmail);

		console.log('🧪 Test user created for authentication type validation');
	});

	afterAll(async () => {
		// Cleanup all created users
		for (const email of createdUsers) {
			try {
				await TRPCTestHelper.cleanupTestUser(email);
			} catch (error) {
				console.warn(`⚠️ Failed to cleanup user: ${email}`, error);
			}
		}
		console.log('🧹 Test user cleanup completed');
	});

	describe('Identity Service Authentication Response Structure', () => {
		it('should return authentication response matching expected structure via direct API', async () => {
			const result = await TRPCTestHelper.loginUserInIdentityService(testUserEmail, testUserPassword);

			// Verify the response structure matches what we expect
			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.user).toBeDefined();

			if (result.tokens) {
				expect(typeof result.tokens.accessToken).toBe('string');
				expect(typeof result.tokens.refreshToken).toBe('string');
				expect(typeof result.tokens.expiresIn).toBe('number');
			}

			if (result.user) {
				expect(typeof result.user.id).toBe('string');
				expect(typeof result.user.email).toBe('string');
				expect(result.user.email).toBe(testUserEmail);
			}

			console.log('✅ Authentication response structure is valid');
		});

		it('should return tokens that can be decoded as AccessTokenPayload', async () => {
			const result = await TRPCTestHelper.loginUserInIdentityService(testUserEmail, testUserPassword);

			expect(result.success).toBe(true);
			expect(result.tokens?.accessToken).toBeDefined();

			if (result.tokens?.accessToken) {
				// Decode the JWT token (using any to access all JWT fields including iat/exp)
				const decoded = jwtDecode<AccessTokenPayload & { iat: number; exp: number }>(result.tokens.accessToken);

				// Verify the token payload structure
				expect(decoded).toHaveProperty('userId');
				expect(decoded).toHaveProperty('email');
				expect(decoded).toHaveProperty('clientId');
				expect(decoded).toHaveProperty('roles');
				expect(decoded).toHaveProperty('iat');
				expect(decoded).toHaveProperty('exp');

				// Verify field types
				expect(typeof decoded.userId).toBe('string');
				expect(typeof decoded.email).toBe('string');
				expect(typeof decoded.clientId).toBe('string');
				expect(Array.isArray(decoded.roles) || typeof decoded.roles === 'number').toBe(true);
				expect(typeof decoded.iat).toBe('number');
				expect(typeof decoded.exp).toBe('number');

				// Verify email matches
				expect(decoded.email).toBe(testUserEmail);

				console.log('✅ Token successfully decoded as AccessTokenPayload:', {
					userId: decoded.userId,
					email: decoded.email,
					clientId: decoded.clientId,
					roles: Array.isArray(decoded.roles) ? decoded.roles.length : decoded.roles
				});
			}
		});
	});

	describe('Registration Response Structure', () => {
		it('should return registration response with proper user structure', async () => {
			const userPrefix = 'reg-structure-test';
			const result = await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);

			if (result.user?.email) {
				createdUsers.push(result.user.email);
			}

			// Verify registration response structure
			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.user).toBeDefined();

			if (result.tokens) {
				expect(typeof result.tokens.accessToken).toBe('string');
				expect(typeof result.tokens.refreshToken).toBe('string');
			}

			if (result.user) {
				expect(typeof result.user.id).toBe('string');
				expect(typeof result.user.email).toBe('string');
				expect(result.user.email).toContain(userPrefix);
			}

			console.log('✅ Registration response structure is valid');
		});
	});

	describe('Direct Identity Service Integration', () => {
		it('should work with the helper methods that wrap direct API calls', async () => {
			// This test verifies that our helper methods work correctly
			// The helper methods already test the direct API integration
			const result = await TRPCTestHelper.loginUserInIdentityService(testUserEmail, testUserPassword);

			// Verify the helper returns the expected structure
			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.user).toBeDefined();
			expect(result.user?.email).toBe(testUserEmail);

			console.log('✅ Direct API integration works through helper methods');
		});
	});

	describe('Error Response Structure', () => {
		it('should return proper error structure for invalid credentials', async () => {
			try {
				await TRPCTestHelper.loginUserInIdentityService('<EMAIL>', 'wrong-password');

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();

				// Verify error structure - just check that error info is available
				expect(error.message || error.toString()).toBeTruthy();

				console.log('✅ Error response structure is valid');
			}
		});
	});
});