import { TEST_CONFIG, generateTestUser, generateTestUserForRegistration } from '@/tests/config/test.config';
import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { IDP_URI_CONSTANTS } from '@/shared/shared.uri.constants';

describe('Identity Service Integration - Registration via tRPC (requires bun dev)', () => {
	let createdUsers: string[] = []; // Track users for cleanup
	let trpcClient: ReturnType<typeof TRPCTestHelper.getTRPCClient>;

	beforeAll(async () => {
		// Initialize tRPC client
		TRPCTestHelper.initialize();
		trpcClient = TRPCTestHelper.getTRPCClient();

		// Check if tRPC server is running
		try {
			await trpcClient.identity.healthCheck.query();
			console.log('✅ tRPC server is running and ready for tests');
		} catch (error) {
			throw new Error('❌ tRPC server is not running. Please start it with: bun dev');
		}

		// Wait for Identity service to be ready
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		console.log('✅ Identity service is ready for tRPC registration tests');
	});

	afterAll(async () => {
		// Cleanup all created users
		for (const email of createdUsers) {
			await TRPCTestHelper.cleanupTestUser(email);
		}
		console.log(`🧹 Cleaned up ${createdUsers.length} test users`);
	});

	describe('User Registration', () => {
		it('should successfully register a new user via Direct API', async () => {
			const userPrefix = 'reg-success';

			const { result, responseTime } = await TRPCTestHelper.measureResponseTime(async () => {
				return await TRPCTestHelper.createTestUserInIdentityService(userPrefix, false);
			});

			// Track for cleanup
			if (result.user?.email) {
				createdUsers.push(result.user.email);
			}

			expect(result.success).toBe(true);
			expect(result.tokens).toBeDefined();
			expect(result.tokens?.accessToken).toBeDefined();
			expect(result.tokens?.refreshToken).toBeDefined();
			expect(result.user).toBeDefined();
			expect(result.user?.email).toContain(userPrefix);

			// Check performance
			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.REGISTRATION_MAX_TIME);
			console.log(`✅ Registration completed in ${responseTime}ms`);
		});

		it('should fail registration with duplicate email via Direct API', async () => {
			// First registration should succeed
			const firstResult = await TRPCTestHelper.createTestUserInIdentityService('reg-duplicate', false);
			expect(firstResult.success).toBe(true);
			expect(firstResult.user?.email).toBeDefined();

			const registeredEmail = firstResult.user!.email;
			createdUsers.push(registeredEmail);

			// Second registration with the exact same email should fail
			try {
				// Create new user data but use the same email
				const duplicateUserData = generateTestUserForRegistration('reg-duplicate-attempt');
				duplicateUserData.email = registeredEmail; // Use the exact same email

				const request = TRPCTestHelper.getIdentityServiceRequest();
				const response = await request
					.post(`/${IDP_URI_CONSTANTS.AUTH.REGISTER}`)
					.send(duplicateUserData)
					.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

				// Check if the response indicates failure (even with 200 status)
				if (response.body && response.body.success === false) {
					// This is the expected behavior - duplicate email rejected
					expect(response.body.message).toMatch(/email|duplicate|conflict|exists/i);
					console.log('✅ Duplicate email correctly rejected');
				} else {
					// If we get here, the registration succeeded when it should have failed
					console.log('❌ Duplicate registration unexpectedly succeeded:', response.body);
					expect(true).toBe(false);
				}
			} catch (error: any) {
				// Also handle cases where an HTTP error is thrown
				expect(error).toBeDefined();
				expect(error.message || error.toString()).toMatch(/email|duplicate|conflict|exists/i);
				console.log('✅ Duplicate email correctly rejected via exception');
			}
		});

		it('should fail registration with invalid email format via tRPC', async () => {
			try {
				await trpcClient.identity.register.mutate({
					email: 'invalid-email-format',
					password: TEST_CONFIG.TEST_DATA.VALID_PASSWORD,
					firstName: 'Test',
					lastName: 'User'
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toContain('email');
				console.log('✅ Invalid email format correctly rejected');
			}
		});

		it('should fail registration with weak password via tRPC', async () => {
			const testUser = generateTestUser('reg-weak-pass');

			try {
				await trpcClient.identity.register.mutate({
					email: testUser.email,
					password: 'weak',
					firstName: 'Test',
					lastName: 'User'
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toBeDefined();
				console.log('✅ Weak password correctly rejected');
			}
		});

		it('should fail registration with missing required fields via tRPC', async () => {
			const testUser = generateTestUser('reg-missing-fields');

			try {
				await trpcClient.identity.register.mutate({
					email: testUser.email,
					password: testUser.password,
					firstName: '', // Empty required field
					lastName: 'User'
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toBeDefined();
				console.log('✅ Missing required fields correctly rejected');
			}
		});

		it('should handle optional company field correctly via tRPC', async () => {
			const testUser = generateTestUser('reg-no-company');

			const result = await trpcClient.identity.register.mutate({
				email: testUser.email,
				password: testUser.password,
				firstName: 'Test',
				lastName: 'User'
				// No company field
			});

			createdUsers.push(testUser.email);

			expect(result).toBeDefined();
			expect(result.email).toBe(testUser.email);
			expect(result.firstName).toBe('Test');
			expect(result.lastName).toBe('User');
			expect(result.company).toBeUndefined();

			console.log('✅ Optional company field handled correctly');
		});
	});

	describe('Registration Validation', () => {
		it('should validate email domain restrictions if any', async () => {
			const testUser = generateTestUser('reg-domain');
			// Use a different domain to test restrictions
			const invalidDomainEmail = testUser.email.replace('@excelytics.com', '@invalid-domain.com');

			try {
				await trpcClient.identity.register.mutate({
					email: invalidDomainEmail,
					password: testUser.password,
					firstName: 'Test',
					lastName: 'User'
				});

				// If no domain restrictions, this should succeed
				createdUsers.push(invalidDomainEmail);
				console.log('✅ No domain restrictions enforced');
			} catch (error: any) {
				// If domain restrictions exist, should be rejected
				expect(error).toBeDefined();
				console.log('✅ Domain restrictions enforced:', error.message);
			}
		});

		it('should validate password complexity requirements', async () => {
			const testUser = generateTestUser('reg-password-complex');

			const weakPasswords = [
				'123456', // Too simple
				'password', // Common word
				'Password', // Missing special chars
				'Password123', // Missing special chars
				'Pass@1' // Too short
			];

			for (const weakPassword of weakPasswords) {
				try {
					await trpcClient.identity.register.mutate({
						email: generateTestUser(`weak-${weakPasswords.indexOf(weakPassword)}`).email,
						password: weakPassword,
						firstName: 'Test',
						lastName: 'User'
					});

					console.log(`⚠️ Weak password "${weakPassword}" was accepted`);
				} catch (error: any) {
					console.log(`✅ Weak password "${weakPassword}" correctly rejected`);
				}
			}
		});

		it('should handle special characters in names correctly', async () => {
			const testUser = generateTestUser('reg-special-chars');

			const result = await trpcClient.identity.register.mutate({
				email: testUser.email,
				password: testUser.password,
				firstName: 'Jean-François',
				lastName: "O'Connor",
				company: 'Müller & Associates'
			});

			createdUsers.push(testUser.email);

			expect(result).toBeDefined();
			expect(result.firstName).toBe('Jean-François');
			expect(result.lastName).toBe("O'Connor");
			expect(result.company).toBe('Müller & Associates');

			console.log('✅ Special characters in names handled correctly');
		});
	});

	describe('Registration Performance and Reliability', () => {
		it('should handle multiple concurrent registrations', async () => {
			const concurrentRegistrations = Array(3)
				.fill(null)
				.map((_, index) => {
					const testUser = generateTestUser(`concurrent-${index}`);
					createdUsers.push(testUser.email);

					return TRPCTestHelper.measureResponseTime(async () => {
						return await trpcClient.identity.register.mutate({
							email: testUser.email,
							password: testUser.password,
							firstName: `Test${index}`,
							lastName: 'User'
						});
					});
				});

			const results = await Promise.all(concurrentRegistrations);

			results.forEach((result, index) => {
				expect(result.result).toBeDefined();
				expect(result.result.firstName).toBe(`Test${index}`);
				expect(result.responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.REGISTRATION_MAX_TIME);
				console.log(`✅ Concurrent registration ${index + 1} completed in ${result.responseTime}ms`);
			});
		});

		it('should maintain consistent response structure', async () => {
			const testUser = generateTestUser('reg-structure');

			const result = await trpcClient.identity.register.mutate({
				email: testUser.email,
				password: testUser.password,
				firstName: 'Test',
				lastName: 'User',
				company: 'Test Company'
			});

			createdUsers.push(testUser.email);

			// Verify response structure
			expect(result).toHaveProperty('id');
			expect(result).toHaveProperty('email');
			expect(result).toHaveProperty('firstName');
			expect(result).toHaveProperty('lastName');
			expect(result).toHaveProperty('createdAt');
			expect(result).toHaveProperty('emailVerified');

			expect(typeof result.id).toBe('string');
			expect(typeof result.email).toBe('string');
			expect(typeof result.firstName).toBe('string');
			expect(typeof result.lastName).toBe('string');
			expect(typeof result.createdAt).toBe('string');
			expect(typeof result.emailVerified).toBe('boolean');

			console.log('✅ Response structure is consistent');
		});
	});
});