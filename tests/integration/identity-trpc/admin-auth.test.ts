import { AccessTokenPayload, EnumTokenType } from 'excelytics.shared-dtos';
import { afterAll, beforeAll, describe, expect, it } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { jwtDecode } from 'jwt-decode';

describe('Identity Admin vs Regular User Authentication', () => {
	let adminUser: any;
	let regularUser: any;
	let adminToken: string;
	let regularUserToken: string;

	beforeAll(async () => {
		// Wait for Identity service to be ready
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		// Create admin user
		console.log('🔑 Creating admin user...');
		const adminResult = await TRPCTestHelper.createAdminUser('admin-test');
		if (!adminResult.success) {
			throw new Error(`Failed to create admin user: ${adminResult.error}`);
		}
		adminUser = adminResult.user;
		adminToken = adminResult.tokens!.accessToken;

		// Create regular user
		console.log('👤 Creating regular user...');
		const regularResult = await TRPCTestHelper.createRegularUser('regular-test');
		if (!regularResult.success) {
			throw new Error(`Failed to create regular user: ${regularResult.error}`);
		}
		regularUser = regularResult.user;
		regularUserToken = regularResult.tokens!.accessToken;

		console.log('✅ Test users created successfully');
	});

	afterAll(async () => {
		// Cleanup all test users using admin token
		await TRPCTestHelper.cleanupAllTestUsers();
		console.log('🧹 All test users cleaned up');
	});

	describe('Token Storage and Management', () => {
		it('should store admin and regular user sessions correctly', () => {
			// Verify admin user session is stored
			const adminSession = TRPCTestHelper.getUserSession(adminUser.email);
			expect(adminSession).toBeDefined();
			expect(adminSession?.isAdmin).toBe(true);
			expect(adminSession?.tokens.accessToken).toBe(adminToken);

			// Verify regular user session is stored
			const regularSession = TRPCTestHelper.getUserSession(regularUser.email);
			expect(regularSession).toBeDefined();
			expect(regularSession?.isAdmin).toBe(false);
			expect(regularSession?.tokens.accessToken).toBe(regularUserToken);

			// Verify admin token is stored globally
			const storedAdminToken = TRPCTestHelper.getAdminToken();
			expect(storedAdminToken).toBe(adminToken);

			console.log('✅ User sessions stored correctly');
		});

		it('should decode JWT tokens and verify user roles', () => {
			// Decode admin token
			const adminPayload = jwtDecode<AccessTokenPayload>(adminToken);
			expect(adminPayload.email).toBe(adminUser.email);
			expect(adminPayload.roles).toBeDefined();
			expect(Array.isArray(adminPayload.roles)).toBe(true);

			// Decode regular user token
			const regularPayload = jwtDecode<AccessTokenPayload>(regularUserToken);
			expect(regularPayload.email).toBe(regularUser.email);
			expect(regularPayload.roles).toBeDefined();
			expect(Array.isArray(regularPayload.roles)).toBe(true);

			console.log('✅ JWT tokens decoded successfully:', {
				admin: {
					email: adminPayload.email,
					roles: adminPayload.roles?.length
				},
				regular: {
					email: regularPayload.email,
					roles: regularPayload.roles?.length
				}
			});
		});
	});

	describe('Authentication with Stored Tokens', () => {
		it('should login admin user and update stored session', async () => {
			const loginResult = await TRPCTestHelper.loginUserInIdentityService(adminUser.email, 'TestPassword@123');

			expect(loginResult.success).toBe(true);
			expect(loginResult.tokens).toBeDefined();
			expect(loginResult.user?.email).toBe(adminUser.email);

			// Verify session was updated
			const updatedSession = TRPCTestHelper.getUserSession(adminUser.email);
			expect(updatedSession?.tokens.accessToken).toBe(loginResult.tokens!.accessToken);

			console.log('✅ Admin user login and session update successful');
		});

		it('should login regular user and update stored session', async () => {
			const loginResult = await TRPCTestHelper.loginUserInIdentityService(regularUser.email, 'TestPassword@123');

			expect(loginResult.success).toBe(true);
			expect(loginResult.tokens).toBeDefined();
			expect(loginResult.user?.email).toBe(regularUser.email);

			// Verify session was updated
			const updatedSession = TRPCTestHelper.getUserSession(regularUser.email);
			expect(updatedSession?.tokens.accessToken).toBe(loginResult.tokens!.accessToken);

			console.log('✅ Regular user login and session update successful');
		});
	});

	describe('Admin User Deletion Permissions', () => {
		it('should attempt to delete users with admin token', async () => {
			// Create a temporary user to delete
			const tempUserResult = await TRPCTestHelper.createRegularUser('temp-delete-test');
			expect(tempUserResult.success).toBe(true);

			const tempUser = tempUserResult.user!;
			console.log(`👤 Created temporary user for deletion test: ${tempUser.email}`);

			// Attempt to delete the user with admin token
			const deleteResult = await TRPCTestHelper.cleanupTestUser(tempUser.email, adminToken);

			// Log the result for debugging - may succeed or fail depending on permissions
			console.log('🔍 Delete attempt result:', {
				success: deleteResult.success,
				status: deleteResult.status,
				error: deleteResult.error
			});

			// The test passes if we get a response (success or failure)
			expect(deleteResult).toBeDefined();
			expect(typeof deleteResult.success).toBe('boolean');

			console.log('✅ Admin deletion attempt completed');
		});

		it('should track all stored user emails', () => {
			const storedEmails = TRPCTestHelper.getStoredUserEmails();
			expect(storedEmails).toContain(adminUser.email);
			expect(storedEmails).toContain(regularUser.email);
			expect(storedEmails.length).toBeGreaterThanOrEqual(2);

			console.log('✅ Stored user emails tracked correctly:', storedEmails.length);
		});
	});

	describe('Token Validation and Structure', () => {
		it('should validate AccessTokenPayload structure for admin user', () => {
			const payload = jwtDecode<AccessTokenPayload>(adminToken);

			// Verify all required AccessTokenPayload fields
			expect(payload).toHaveProperty('userId');
			expect(payload).toHaveProperty('email');
			expect(payload).toHaveProperty('clientId');
			expect(payload).toHaveProperty('clientOrigin');
			expect(payload).toHaveProperty('roles');
			expect(payload).toHaveProperty('tokenType');
			expect(payload).toHaveProperty('iat');
			expect(payload).toHaveProperty('exp');

			// Verify token type (lowercase in actual implementation)
			expect(payload.tokenType).toBe(EnumTokenType.ACCESS);
			expect(payload.email).toBe(adminUser.email);

			console.log('✅ Admin token AccessTokenPayload structure valid');
		});

		it('should validate AccessTokenPayload structure for regular user', () => {
			const payload = jwtDecode<AccessTokenPayload>(regularUserToken);

			// Verify all required AccessTokenPayload fields
			expect(payload).toHaveProperty('userId');
			expect(payload).toHaveProperty('email');
			expect(payload).toHaveProperty('clientId');
			expect(payload).toHaveProperty('clientOrigin');
			expect(payload).toHaveProperty('roles');
			expect(payload).toHaveProperty('tokenType');
			expect(payload).toHaveProperty('iat');
			expect(payload).toHaveProperty('exp');

			// Verify token type (lowercase in actual implementation)
			expect(payload.tokenType).toBe(EnumTokenType.ACCESS);
			expect(payload.email).toBe(regularUser.email);

			console.log('✅ Regular user token AccessTokenPayload structure valid');
		});
	});

	describe('Error Handling', () => {
		it('should handle invalid login credentials', async () => {
			const loginResult = await TRPCTestHelper.loginUserInIdentityService(regularUser.email, 'invalid-password');

			expect(loginResult.success).toBe(false);

			// Error might be in different places depending on response structure
			const hasError = loginResult.error || loginResult.status >= 400;
			expect(hasError).toBeTruthy();

			console.log('✅ Invalid credentials properly rejected:', {
				success: loginResult.success,
				status: loginResult.status,
				hasError: !!hasError
			});
		});

		it('should handle cleanup without admin token gracefully', async () => {
			// Try to cleanup without admin token (should warn but not fail)
			const result = await TRPCTestHelper.cleanupTestUser('<EMAIL>');

			// Should not throw an error, but may not succeed without proper auth
			expect(result).toBeDefined();
			expect(typeof result.success).toBe('boolean');

			console.log('✅ Cleanup without admin token handled gracefully');
		});
	});
});