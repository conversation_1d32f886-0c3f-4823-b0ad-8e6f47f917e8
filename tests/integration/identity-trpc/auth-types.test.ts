import { AccessTokenPayload, EnumTokenType } from 'excelytics.shared-dtos';
import { afterAll, beforeAll, describe, expect, it } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { jwtDecode } from 'jwt-decode';

describe('Identity Authentication Type Validation', () => {
	let testUser: any;
	let trpcClient: ReturnType<typeof TRPCTestHelper.getTRPCClient>;

	beforeAll(async () => {
		// Wait for Identity service to be ready
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		// Create test user (regular user)
		const userResult = await TRPCTestHelper.createRegularUser('auth-types-test');
		if (!userResult.success) {
			console.error('User creation failed:', userResult);
			throw new Error(
				`Failed to create test user: ${userResult.error || 'Unknown error'} (Status: ${userResult.status})`
			);
		}

		testUser = userResult.user;
		trpcClient = TRPCTestHelper.getTRPCClient();
		console.log('🧪 Test user created for authentication type validation');
	});

	afterAll(async () => {
		// Cleanup all test users
		await TRPCTestHelper.cleanupAllTestUsers();
		console.log('🧹 Test user cleanup completed');
	});

	describe('Identity Service Authentication Response Structure', () => {
		it('should return authentication response matching expected structure via direct API', async () => {
			const result = await TRPCTestHelper.loginUserInIdentityService(
				testUser.email,
				testUser.password || 'TestPassword@123'
			);

			// Verify response structure matches expected format
			expect(result).toBeDefined();
			expect(result.success).toBe(true);
			expect(result).toHaveProperty('user');
			expect(result).toHaveProperty('tokens');

			// Verify user object structure
			expect(result.user).toHaveProperty('id');
			expect(result.user).toHaveProperty('email');
			expect(result.user?.email).toBe(testUser.email);

			// Verify tokens structure
			expect(result.tokens).toHaveProperty('accessToken');
			expect(result.tokens).toHaveProperty('refreshToken');

			// Verify token types
			expect(typeof result.tokens?.accessToken).toBe('string');
			expect(typeof result.tokens?.refreshToken).toBe('string');

			console.log('✅ Authentication response structure is valid');
		});

		it('should return tokens that can be decoded as AccessTokenPayload', async () => {
			const result = await TRPCTestHelper.loginUserInIdentityService(
				testUser.email,
				testUser.password || 'TestPassword@123'
			);

			// The accessToken should be a real JWT that we can decode
			expect(result.tokens?.accessToken).toBeDefined();
			expect(result.tokens?.accessToken.length).toBeGreaterThan(0);

			// Decode the actual JWT token
			try {
				const decoded = jwtDecode<AccessTokenPayload>(result.tokens!.accessToken);

				// Verify the decoded payload has all required AccessTokenPayload properties
				expect(decoded).toHaveProperty('userId');
				expect(decoded).toHaveProperty('email');
				expect(decoded).toHaveProperty('clientId');
				expect(decoded).toHaveProperty('clientOrigin');
				expect(decoded).toHaveProperty('roles');
				expect(decoded).toHaveProperty('tokenType');
				expect(decoded.tokenType).toBe(EnumTokenType.ACCESS);
				expect(decoded.email).toBe(testUser.email);

				console.log('✅ Token successfully decoded as AccessTokenPayload:', {
					userId: decoded.userId,
					email: decoded.email,
					clientId: decoded.clientId,
					roles: decoded.roles?.length
				});
			} catch (error) {
				console.error('Failed to decode JWT token:', error);
				throw new Error('Token is not a valid JWT');
			}
		});

		it('should return user data compatible with Express Request.user type', async () => {
			const result = await trpcClient.identity.login.mutate({
				email: testUser.email,
				password: testUser.password || 'TestPassword@123',
				rememberMe: false
			});

			// The user object should be compatible with AccessTokenPayload
			// (minus the tokenType which is omitted in the User type)
			const userForRequest = {
				userId: result.user.id,
				email: result.user.email,
				clientId: 'test-client-id',
				clientOrigin: testUser.clientOrigin || 1,
				roles: ['user'],
				iat: Math.floor(Date.now() / 1000),
				exp: Math.floor(Date.now() / 1000) + 3600
				// tokenType is omitted as per User type definition
			};

			// Verify compatibility with Express Request.user type
			expect(userForRequest).toHaveProperty('userId');
			expect(userForRequest).toHaveProperty('email');
			expect(userForRequest).toHaveProperty('clientId');
			expect(userForRequest).toHaveProperty('clientOrigin');
			expect(userForRequest).toHaveProperty('roles');
			expect(userForRequest).not.toHaveProperty('tokenType');

			console.log('✅ User data is compatible with Express Request.user type');
		});
	});

	describe('Registration Response Structure', () => {
		it('should return registration response with proper user structure', async () => {
			const registrationResult = await trpcClient.identity.register.mutate({
				email: `reg-test-${Date.now()}@excelytics.com`,
				password: 'TestPassword@123',
				firstName: 'Registration',
				lastName: 'Test',
				company: 'Test Company'
			});

			// Verify registration response structure
			expect(registrationResult).toBeDefined();
			expect(registrationResult).toHaveProperty('id');
			expect(registrationResult).toHaveProperty('email');
			expect(registrationResult).toHaveProperty('firstName');
			expect(registrationResult).toHaveProperty('lastName');
			expect(registrationResult).toHaveProperty('createdAt');
			expect(registrationResult).toHaveProperty('emailVerified');

			// Verify data types
			expect(typeof registrationResult.id).toBe('string');
			expect(typeof registrationResult.email).toBe('string');
			expect(typeof registrationResult.firstName).toBe('string');
			expect(typeof registrationResult.lastName).toBe('string');
			expect(typeof registrationResult.emailVerified).toBe('boolean');

			console.log('✅ Registration response structure is valid');
		});
	});

	describe('Direct Identity Service Integration', () => {
		it('should match tRPC response format with direct Identity service calls', async () => {
			// Test direct Identity service call
			const directResult = await TRPCTestHelper.loginUserInIdentityService(
				testUser.email,
				testUser.password || 'TestPassword@123'
			);

			// Test tRPC call
			const trpcResult = await trpcClient.identity.login.mutate({
				email: testUser.email,
				password: testUser.password || 'TestPassword@123',
				rememberMe: false
			});

			// Both should have similar structure
			expect(directResult.success).toBe(true);
			expect(directResult.user).toBeDefined();
			expect(directResult.tokens).toBeDefined();

			expect(trpcResult.user).toBeDefined();
			expect(trpcResult.tokens).toBeDefined();

			// Verify both have required fields
			expect(directResult.user?.email).toBe(testUser.email);
			expect(trpcResult.user.email).toBe(testUser.email);

			console.log('✅ tRPC and direct Identity service responses are compatible');
		});
	});

	describe('Error Response Structure', () => {
		it('should return proper error structure for invalid credentials', async () => {
			try {
				await trpcClient.identity.login.mutate({
					email: testUser.email,
					password: 'invalid-password',
					rememberMe: false
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				// Verify error structure
				expect(error).toBeDefined();
				expect(error.message).toBeDefined();

				// tRPC errors should have specific structure
				expect(error).toHaveProperty('data');

				console.log('✅ Error response structure is valid');
			}
		});
	});
});