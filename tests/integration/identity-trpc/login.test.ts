import { TEST_CONFIG, generateTestUser } from '@/tests/config/test.config';
import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';

describe('Identity Service Integration - Login via tRPC', () => {
	let testUser: ReturnType<typeof generateTestUser>;
	let trpcClient: ReturnType<typeof TRPCTestHelper.getTRPCClient>;

	beforeAll(async () => {
		// Initialize test helper
		TRPCTestHelper.initialize();
		trpcClient = TRPCTestHelper.getTRPCClient();

		// Wait for Identity service to be ready
		const isReady = await TRPCTestHelper.waitForIdentityService();
		if (!isReady) {
			throw new Error('Identity service is not available for testing');
		}

		// Create a test user for login tests
		const createResult = await TRPCTestHelper.createTestUserInIdentityService('login-test');

		if (!createResult.success) {
			console.warn('Failed to create test user, tests may fail:', createResult.error);
			// Fallback to generated user for cleanup
			testUser = generateTestUser('login-test');
		} else {
			// Use the actual user data from the Identity service
			testUser = {
				email: createResult.user!.email,
				password: TEST_CONFIG.TEST_DATA.VALID_PASSWORD, // Use the same password that was used for registration
				clientOrigin: TEST_CONFIG.TEST_DATA.CLIENT_ORIGIN,
				isActive: true
			};
			console.log('✅ Test user created successfully:', testUser.email);
		}
	});

	afterAll(async () => {
		// Cleanup test user
		if (testUser) {
			await TRPCTestHelper.cleanupTestUser(testUser.email);
			console.log('🧹 Test user cleanup completed');
		}
	});

	describe('Login Functionality', () => {
		it('should successfully login with valid credentials via tRPC', async () => {
			const { result, responseTime } = await TRPCTestHelper.measureResponseTime(async () => {
				return await trpcClient.identity.login.mutate({
					email: testUser.email,
					password: testUser.password,
					rememberMe: false
				});
			});

			expect(result).toBeDefined();
			expect(result.user).toBeDefined();
			expect(result.user.email).toBe(testUser.email);
			expect(result.tokens).toBeDefined();
			expect(result.tokens.accessToken).toBeDefined();
			expect(result.tokens.refreshToken).toBeDefined();
			expect(result.tokens.expiresIn).toBeGreaterThan(0);

			// Check performance
			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.LOGIN_MAX_TIME);
			console.log(`✅ Login completed in ${responseTime}ms`);
		});

		it('should fail login with invalid password via tRPC', async () => {
			try {
				await trpcClient.identity.login.mutate({
					email: testUser.email,
					password: 'WrongPassword123!',
					rememberMe: false
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toBeDefined();
				console.log('✅ Invalid password correctly rejected');
			}
		});

		it('should fail login with non-existent email via tRPC', async () => {
			try {
				await trpcClient.identity.login.mutate({
					email: '<EMAIL>',
					password: testUser.password,
					rememberMe: false
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toBeDefined();
				console.log('✅ Non-existent email correctly rejected');
			}
		});

		it('should fail login with invalid email format via tRPC', async () => {
			try {
				await trpcClient.identity.login.mutate({
					email: 'invalid-email-format',
					password: testUser.password,
					rememberMe: false
				});

				// Should not reach here
				expect(true).toBe(false);
			} catch (error: any) {
				expect(error).toBeDefined();
				expect(error.message).toContain('email');
				console.log('✅ Invalid email format correctly rejected');
			}
		});

		it('should handle rememberMe option correctly via tRPC', async () => {
			const result = await trpcClient.identity.login.mutate({
				email: testUser.email,
				password: testUser.password,
				rememberMe: true
			});

			expect(result).toBeDefined();
			expect(result.user).toBeDefined();
			expect(result.tokens).toBeDefined();
			expect(result.tokens.accessToken).toBeDefined();
			expect(result.tokens.refreshToken).toBeDefined();

			// With rememberMe, refresh token should have longer expiry
			// This is implementation-specific, adjust based on your IdP logic
			console.log('✅ RememberMe option handled correctly');
		});
	});

	describe('Login Performance and Reliability', () => {
		it('should handle multiple concurrent login requests', async () => {
			const concurrentLogins = Array(5)
				.fill(null)
				.map(() =>
					TRPCTestHelper.measureResponseTime(async () => {
						return await trpcClient.identity.login.mutate({
							email: testUser.email,
							password: testUser.password,
							rememberMe: false
						});
					})
				);

			const results = await Promise.all(concurrentLogins);

			results.forEach((result, index) => {
				expect(result.result).toBeDefined();
				expect(result.result.user.email).toBe(testUser.email);
				expect(result.responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.LOGIN_MAX_TIME);
				console.log(`✅ Concurrent login ${index + 1} completed in ${result.responseTime}ms`);
			});
		});

		it('should maintain consistent response structure', async () => {
			const result = await trpcClient.identity.login.mutate({
				email: testUser.email,
				password: testUser.password,
				rememberMe: false
			});

			// Verify response structure
			expect(result).toHaveProperty('user');
			expect(result).toHaveProperty('tokens');

			expect(result.user).toHaveProperty('id');
			expect(result.user).toHaveProperty('email');
			expect(result.user).toHaveProperty('firstName');
			expect(result.user).toHaveProperty('lastName');

			expect(result.tokens).toHaveProperty('accessToken');
			expect(result.tokens).toHaveProperty('refreshToken');
			expect(result.tokens).toHaveProperty('expiresIn');

			console.log('✅ Response structure is consistent');
		});
	});

	describe('Error Handling', () => {
		it('should handle network timeouts gracefully', async () => {
			// This test simulates network issues
			// In a real scenario, you might temporarily stop the Identity service
			console.log('⚠️ Network timeout test - requires manual service interruption');

			try {
				const result = await trpcClient.identity.login.mutate({
					email: testUser.email,
					password: testUser.password,
					rememberMe: false
				});

				// If service is running, this should succeed
				expect(result).toBeDefined();
				console.log('✅ Service is available, login succeeded');
			} catch (error: any) {
				// If service is down, should handle gracefully
				expect(error).toBeDefined();
				console.log('✅ Network error handled gracefully:', error.message);
			}
		});

		it('should provide meaningful error messages', async () => {
			try {
				await trpcClient.identity.login.mutate({
					email: testUser.email,
					password: 'WrongPassword',
					rememberMe: false
				});

				expect(true).toBe(false); // Should not reach here
			} catch (error: any) {
				expect(error.message).toBeDefined();
				expect(error.message.length).toBeGreaterThan(0);
				expect(typeof error.message).toBe('string');
				console.log('✅ Error message provided:', error.message);
			}
		});
	});
});