import { createSimpleHealthCheckHandler } from '@/tests/helpers/health-check.helper';
import { describe, it, expect, beforeAll } from 'bun:test';
import { TEST_CONFIG } from '@/tests/config/test.config';

describe('Finance Client - Health Checks', () => {
	beforeAll(async () => {
		console.log('🏥 Starting Finance Client health check tests');
		console.log('✅ Health check system initialized');
	});

	describe('Basic Health Check', () => {
		it('should return healthy status', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			const startTime = Date.now();
			const result = await healthCheckHandler();
			const responseTime = Date.now() - startTime;

			expect(result).toBeDefined();
			expect(result.success).toBe(true);
			expect(result.data.status).toBe('healthy');
			expect(result.data.service).toBe('excelytics.finance.client');
			expect(result.data.timestamp).toBeDefined();
			expect(result.data.version).toBeDefined();

			// Check performance
			expect(responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.HEALTH_CHECK_MAX_TIME);
			console.log(`✅ Health check completed in ${responseTime}ms`);
			console.log(`📊 Service version: ${result.data.version}`);
		});

		it('should have consistent response structure', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			const result = await healthCheckHandler();

			expect(result.success).toBe(true);
			expect(result.data).toBeDefined();

			// Verify required fields
			expect(result.data).toHaveProperty('status');
			expect(result.data).toHaveProperty('service');
			expect(result.data).toHaveProperty('timestamp');
			expect(result.data).toHaveProperty('version');
			expect(result.data).toHaveProperty('responseTime');
			expect(result.data).toHaveProperty('dependencies');

			// Verify field types
			expect(typeof result.data.status).toBe('string');
			expect(typeof result.data.service).toBe('string');
			expect(result.data.timestamp instanceof Date).toBe(true);
			expect(typeof result.data.version).toBe('string');
			expect(typeof result.data.responseTime).toBe('number');
			expect(Array.isArray(result.data.dependencies)).toBe(true);

			console.log('✅ Health check response structure is consistent');
		});

		it('should respond quickly under load', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			const concurrentHealthChecks = Array(10)
				.fill(null)
				.map(async () => {
					const startTime = Date.now();
					const result = await healthCheckHandler();
					const responseTime = Date.now() - startTime;
					return { result, responseTime };
				});

			const results = await Promise.all(concurrentHealthChecks);

			results.forEach((testResult, index) => {
				expect(testResult.result.success).toBe(true);
				expect(testResult.result.data.status).toBe('healthy');
				expect(testResult.responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.HEALTH_CHECK_MAX_TIME);
				console.log(`✅ Concurrent health check ${index + 1} completed in ${testResult.responseTime}ms`);
			});

			const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
			console.log(`📊 Average response time under load: ${avgResponseTime.toFixed(2)}ms`);
		});
	});

	describe('Deep Health Check', () => {
		it('should return comprehensive health status', async () => {
			// Create a deep health check handler
			const deepHealthHandler = createSimpleHealthCheckHandler(
				'excelytics.finance.client',
				'1.0.0',
				true // deep health check
			);

			const startTime = Date.now();
			const result = await deepHealthHandler();
			const responseTime = Date.now() - startTime;

			expect(result.success).toBe(true);
			expect(result.data).toBeDefined();
			expect(result.data.status).toBe('healthy');
			expect(result.data.dependencies).toBeDefined();
			expect(Array.isArray(result.data.dependencies)).toBe(true);

			// Check if dependencies are present
			if (result.data.dependencies && result.data.dependencies.length > 0) {
				result.data.dependencies.forEach((dependency, index) => {
					expect(dependency).toHaveProperty('name');
					expect(dependency).toHaveProperty('status');
					console.log(`📊 Dependency ${index + 1}: ${dependency.name} - ${dependency.status}`);
				});
			}

			console.log(`✅ Deep health check completed in ${responseTime}ms`);
			console.log(`📊 Overall status: ${result.data.status}`);
		});

		it('should handle error conditions gracefully', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			// Test that our health check system handles errors properly
			const result = await healthCheckHandler();

			// Even if there are issues, the health check should return a structured response
			expect(result).toBeDefined();
			expect(result).toHaveProperty('success');
			expect(result).toHaveProperty('message');
			expect(result).toHaveProperty('data');

			console.log('✅ Error handling works correctly');
		});
	});

	describe('Health Check Performance Monitoring', () => {
		it('should maintain consistent performance over time', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			const iterations = 5;
			const responseTimes: number[] = [];

			for (let i = 0; i < iterations; i++) {
				const startTime = Date.now();
				const result = await healthCheckHandler();
				const responseTime = Date.now() - startTime;

				expect(result.success).toBe(true);
				expect(result.data.status).toBe('healthy');
				responseTimes.push(responseTime);

				// Small delay between iterations
				await new Promise(resolve => setTimeout(resolve, 100));
			}

			// Calculate performance metrics
			const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
			const maxResponseTime = Math.max(...responseTimes);
			const minResponseTime = Math.min(...responseTimes);

			console.log(`📊 Performance over ${iterations} iterations:`);
			console.log(`   - Average: ${avgResponseTime.toFixed(2)}ms`);
			console.log(`   - Min: ${minResponseTime}ms`);
			console.log(`   - Max: ${maxResponseTime}ms`);

			// Performance should be consistent
			expect(avgResponseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.HEALTH_CHECK_MAX_TIME);
			expect(maxResponseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.HEALTH_CHECK_MAX_TIME * 2);
		});

		it('should handle rapid successive health checks', async () => {
			const healthCheckHandler = createSimpleHealthCheckHandler('excelytics.finance.client', '1.0.0', false);

			const rapidChecks = Array(20)
				.fill(null)
				.map(async () => {
					const startTime = Date.now();
					const result = await healthCheckHandler();
					const responseTime = Date.now() - startTime;
					return { result, responseTime };
				});

			const results = await Promise.all(rapidChecks);

			results.forEach(testResult => {
				expect(testResult.result.success).toBe(true);
				expect(testResult.result.data.status).toBe('healthy');
				expect(testResult.responseTime).toBeLessThan(TEST_CONFIG.PERFORMANCE.HEALTH_CHECK_MAX_TIME * 2); // Allow 2x normal time under load
			});

			const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
			console.log(`✅ Handled ${results.length} rapid health checks`);
			console.log(`📊 Average response time: ${avgResponseTime.toFixed(2)}ms`);
		});
	});
});