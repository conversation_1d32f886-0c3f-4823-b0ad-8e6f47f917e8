/**
 * Integration Test Runner for tRPC Identity Service Tests
 *
 * This script runs integration tests against the actual Identity service
 * and provides detailed reporting of test results.
 */
import { checkServiceAvailability } from '@/tests/setup';
import { TEST_CONFIG } from '@/tests/config/test.config';
import { TestResult } from '@/shared/test.types';
import { spawn } from 'child_process';

class IntegrationTestRunner {
	private results: TestResult[] = [];
	private startTime: number = 0;

	async run() {
		console.log('🚀 Starting tRPC Integration Test Suite');
		console.log('='.repeat(60));

		this.startTime = Date.now();

		// Check service availability first
		const serviceAvailable = await checkServiceAvailability();
		if (!serviceAvailable) {
			console.log('\n❌ Cannot run integration tests - Identity service is not available');
			console.log('\n📋 Prerequisites:');
			console.log('1. Start the Identity service on:', TEST_CONFIG.IDENTITY_SERVICE.BASE_URL);
			console.log('2. Ensure the service is healthy and responding');
			console.log('3. Run this test suite again');
			process.exit(1);
		}

		// Run test suites
		await this.runTestSuite('Health Checks', 'tests/integration/identity-health.test.ts');
		await this.runTestSuite('User Registration', 'tests/integration/identity-registration.test.ts');
		await this.runTestSuite('User Login', 'tests/integration/identity-login.test.ts');

		// Generate final report
		this.generateReport();
	}

	private async runTestSuite(name: string, testFile: string): Promise<void> {
		console.log(`\n🧪 Running ${name} Tests`);
		console.log('-'.repeat(40));

		const startTime = Date.now();
		const result: TestResult = {
			name,
			passed: 0,
			failed: 0,
			duration: 0,
			errors: []
		};

		try {
			const testOutput = await this.runBunTest(testFile);
			this.parseTestOutput(testOutput, result);
		} catch (error) {
			result.failed = 1;
			result.errors.push(`Failed to run test suite: ${error}`);
			console.error(`❌ Error running ${name}:`, error);
		}

		result.duration = Date.now() - startTime;
		this.results.push(result);

		// Print immediate results
		if (result.failed === 0) {
			console.log(`✅ ${name}: ${result.passed} tests passed in ${result.duration}ms`);
		} else {
			console.log(`❌ ${name}: ${result.passed} passed, ${result.failed} failed in ${result.duration}ms`);
			result.errors.forEach(error => console.log(`   Error: ${error}`));
		}
	}

	private runBunTest(testFile: string): Promise<string> {
		return new Promise((resolve, reject) => {
			const child = spawn('bun', ['test', testFile], {
				stdio: ['pipe', 'pipe', 'pipe'],
				env: { ...process.env, NODE_ENV: 'test' }
			});

			let stdout = '';
			let stderr = '';

			child.stdout?.on('data', data => {
				stdout += data.toString();
			});

			child.stderr?.on('data', data => {
				stderr += data.toString();
			});

			child.on('close', code => {
				if (code === 0) {
					resolve(stdout);
				} else {
					reject(new Error(`Test failed with code ${code}\nStdout: ${stdout}\nStderr: ${stderr}`));
				}
			});

			child.on('error', error => {
				reject(error);
			});
		});
	}

	private parseTestOutput(output: string, result: TestResult): void {
		// Parse Bun test output to extract pass/fail counts
		const lines = output.split('\n');

		for (const line of lines) {
			// Look for test result patterns
			if (line.includes('✓') || line.includes('PASS')) {
				result.passed++;
			} else if (line.includes('✗') || line.includes('FAIL')) {
				result.failed++;
				result.errors.push(line.trim());
			}
		}

		// If no specific pass/fail indicators found, assume success if no errors
		if (result.passed === 0 && result.failed === 0) {
			if (output.includes('error') || output.includes('Error') || output.includes('FAIL')) {
				result.failed = 1;
				result.errors.push('Test suite completed with errors');
			} else {
				result.passed = 1; // Assume at least one test passed
			}
		}
	}

	private generateReport(): void {
		const totalDuration = Date.now() - this.startTime;
		const totalPassed = this.results.reduce((sum, r) => sum + r.passed, 0);
		const totalFailed = this.results.reduce((sum, r) => sum + r.failed, 0);
		const totalTests = totalPassed + totalFailed;

		console.log('\n' + '='.repeat(60));
		console.log('📊 INTEGRATION TEST REPORT');
		console.log('='.repeat(60));

		// Summary
		console.log(`\n📈 Summary:`);
		console.log(`   Total Tests: ${totalTests}`);
		console.log(`   Passed: ${totalPassed} ✅`);
		console.log(`   Failed: ${totalFailed} ${totalFailed > 0 ? '❌' : '✅'}`);
		console.log(`   Duration: ${totalDuration}ms`);
		console.log(`   Success Rate: ${totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0}%`);

		// Detailed results
		console.log(`\n📋 Detailed Results:`);
		this.results.forEach(result => {
			const status = result.failed === 0 ? '✅' : '❌';
			console.log(
				`   ${status} ${result.name}: ${result.passed}/${result.passed + result.failed} (${result.duration}ms)`
			);

			if (result.errors.length > 0) {
				result.errors.forEach(error => {
					console.log(`      ⚠️  ${error}`);
				});
			}
		});

		// Performance analysis
		console.log(`\n⚡ Performance Analysis:`);
		this.results.forEach(result => {
			const avgTimePerTest = result.duration / Math.max(result.passed + result.failed, 1);
			console.log(`   ${result.name}: ${avgTimePerTest.toFixed(0)}ms avg per test`);
		});

		// Recommendations
		console.log(`\n💡 Recommendations:`);
		if (totalFailed > 0) {
			console.log('   • Review failed tests and fix underlying issues');
			console.log('   • Check Identity service logs for error details');
			console.log('   • Verify service configuration and connectivity');
		} else {
			console.log('   • All tests passed! Consider adding more edge cases');
			console.log('   • Monitor performance trends over time');
		}

		// Service information
		console.log(`\n🔧 Service Information:`);
		console.log(`   Identity Service: ${TEST_CONFIG.IDENTITY_SERVICE.BASE_URL}`);
		console.log(`   API Version: ${TEST_CONFIG.IDENTITY_SERVICE.API_VERSION}`);
		console.log(`   Test Environment: ${process.env.NODE_ENV || 'development'}`);

		console.log('\n' + '='.repeat(60));

		// Exit with appropriate code
		if (totalFailed > 0) {
			console.log('❌ Some tests failed. Check the details above.');
			process.exit(1);
		} else {
			console.log('✅ All integration tests passed successfully!');
			process.exit(0);
		}
	}
}

// Run the test suite if this file is executed directly
if (import.meta.main) {
	const runner = new IntegrationTestRunner();
	runner.run().catch(error => {
		console.error('❌ Test runner failed:', error);
		process.exit(1);
	});
}