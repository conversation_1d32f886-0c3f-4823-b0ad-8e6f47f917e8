// Global test setup for tRPC integration tests
import { TRPCTestHelper } from '@/tests/helpers/trpc-test.helper';
import { TEST_CONFIG } from '@/tests/config/test.config';

// Global test configuration
console.log('🧪 Setting up tRPC integration tests...');

// Initialize test helper
TRPCTestHelper.initialize();

// Set up global test environment
// @ts-ignore
globalThis.testConfig = TEST_CONFIG;

// Helper to check if Identity service is available before running tests
export async function checkServiceAvailability(): Promise<boolean> {
	console.log('🔍 Checking Identity service availability...');

	try {
		const isReady = await TRPCTestHelper.waitForIdentityService(3, 2000);
		if (isReady) {
			console.log('✅ Identity service is available for testing');
			return true;
		} else {
			console.log('❌ Identity service is not available');
			console.log('💡 Make sure the Identity service is running on:', TEST_CONFIG.IDENTITY_SERVICE.BASE_URL);
			return false;
		}
	} catch (error) {
		console.error('❌ Error checking service availability:', error);
		return false;
	}
}

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
	console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', error => {
	console.error('Uncaught Exception:', error);
	process.exit(1);
});

console.log('✅ Test setup completed');

// Export test utilities for use in test files
export { TRPCTestHelper, TEST_CONFIG };