import { EnumClientOrigin } from 'excelytics.shared-dtos';

// Test configuration for tRPC integration tests
export const TEST_CONFIG = {
	// Identity service configuration
	IDENTITY_SERVICE: {
		BASE_URL: process.env.IDP_SERVICE_URL,
		API_VERSION: process.env.API_VERSION,
		TIMEOUT: 10000 // 10 seconds
	},

	// Test user configuration
	TEST_USERS: {
		ADMIN: {
			email: '<EMAIL>',
			password: 'AdminPassword@123',
			roles: ['admin', 'user']
		},
		REGULAR: {
			email: '<EMAIL>',
			password: 'UserPassword@123',
			roles: ['user']
		}
	},

	// Performance thresholds
	PERFORMANCE: {
		HEALTH_CHECK_MAX_TIME: 1000, // 1 second
		LOGIN_MAX_TIME: 3000, // 3 seconds
		REGISTRATION_MAX_TIME: 5000 // 5 seconds
	},

	// Test data
	TEST_DATA: {
		CLIENT_ORIGIN: EnumClientOrigin.Excelytics,
		VALID_PASSWORD: 'TestPassword@123',
		INVALID_PASSWORD: 'weak',
		VALID_EMAIL_DOMAIN: '@excelytics.com'
	}
} as const;

// Helper to generate unique test emails
export function generateTestEmail(prefix: string = 'test'): string {
	const timestamp = Date.now();
	const random = Math.random().toString(36).substring(7);
	return `${prefix}-${timestamp}-${random}${TEST_CONFIG.TEST_DATA.VALID_EMAIL_DOMAIN}`;
}

// Helper to generate test user data
export function generateTestUser(emailPrefix: string = 'test') {
	return {
		email: generateTestEmail(emailPrefix),
		password: TEST_CONFIG.TEST_DATA.VALID_PASSWORD,
		clientOrigin: TEST_CONFIG.TEST_DATA.CLIENT_ORIGIN,
		isActive: true
	};
}

// Helper to generate test user data for registration (matches RegisterDTO)
export function generateTestUserForRegistration(emailPrefix: string = 'test') {
	const baseEmail = generateTestEmail(emailPrefix);
	return {
		email: baseEmail,
		password: TEST_CONFIG.TEST_DATA.VALID_PASSWORD,
		clientId: 'test-client-' + Math.random().toString(36).substring(2, 11),
		clientOrigin: TEST_CONFIG.TEST_DATA.CLIENT_ORIGIN,
		// Additional fields that might be required by RegisterDTO
		firstName: 'Test',
		lastName: 'User',
		company: 'Test Company',
		isActive: true
	};
}