import { SimpleHealthCheckResult, SimpleDependencyHealth, SimpleHealthResponse } from '@/shared/test.types';

/**
 * Simple health check helper for testing
 * Mimics the basic functionality of shared-internals health checks
 */
export class SimpleHealthCheckHelper {
	private serviceName: string;
	private version: string;

	constructor(serviceName: string, version: string) {
		this.serviceName = serviceName;
		this.version = version;
	}

	/**
	 * Performs a basic health check
	 */
	async getHealth(): Promise<SimpleHealthCheckResult> {
		const startTime = process.hrtime();

		// Simulate basic health check
		const dependencies: SimpleDependencyHealth[] = [
			{
				name: 'Self',
				status: 'healthy',
				responseTime: 1
			}
		];

		const diff = process.hrtime(startTime);
		const responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return {
			service: this.serviceName,
			version: this.version,
			timestamp: new Date(),
			status: 'healthy',
			responseTime,
			dependencies
		};
	}

	/**
	 * Performs a deep health check (same as basic for this simple implementation)
	 */
	async getDeepHealth(): Promise<SimpleHealthCheckResult> {
		const startTime = process.hrtime();

		// Simulate deep health check with more dependencies
		const dependencies: SimpleDependencyHealth[] = [
			{
				name: 'Self',
				status: 'healthy',
				responseTime: 1
			},
			{
				name: 'Database',
				status: 'healthy',
				responseTime: 5
			}
		];

		const diff = process.hrtime(startTime);
		const responseTime = parseFloat((diff[0] * 1e3 + diff[1] * 1e-6).toFixed(3));

		return {
			service: this.serviceName,
			version: this.version,
			timestamp: new Date(),
			status: 'healthy',
			responseTime,
			dependencies
		};
	}
}

/**
 * Creates a simple health check handler for testing
 */
export function createSimpleHealthCheckHandler(serviceName: string, version: string, deep: boolean = false) {
	const helper = new SimpleHealthCheckHelper(serviceName, version);

	return async (): Promise<SimpleHealthResponse> => {
		try {
			const healthResult = await (deep ? helper.getDeepHealth() : helper.getHealth());

			return {
				success: healthResult.status === 'healthy',
				message: `Health status: ${healthResult.status}`,
				data: healthResult
			};
		} catch (error: any) {
			return {
				success: false,
				message: 'Health check failed',
				data: {
					service: serviceName,
					version: version,
					timestamp: new Date(),
					status: 'unhealthy',
					responseTime: 0,
					dependencies: [
						{
							name: 'Self',
							status: 'unhealthy',
							error: error.message
						}
					]
				}
			};
		}
	};
}