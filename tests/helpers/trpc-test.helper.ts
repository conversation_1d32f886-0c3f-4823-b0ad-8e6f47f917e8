import { generateTestUserForRegistration, TEST_CONFIG } from '@/tests/config/test.config';
import { AuthResponse, AuthUser, TestUserSession } from '@/shared/test.auth.types';
import { BASE_URLS, IDP_URI_CONSTANTS } from '@/shared/shared.uri.constants';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '@/server/trpc/root';
import * as supertest from 'supertest';
import { jwtDecode } from 'jwt-decode';

// Create a test tRPC client that connects to the actual Identity service
export class TRPCTestHelper {
	private static trpcClient: ReturnType<typeof createTRPCClient<AppRouter>>;
	private static identityServiceRequest: ReturnType<typeof supertest.default>;
	private static testUserSessions: Map<string, TestUserSession> = new Map();
	private static adminToken: string | null = null;

	// Initialize the test helper
	static initialize() {
		// Create tRPC client for testing
		this.trpcClient = createTRPCClient<AppRouter>({
			links: [
				httpBatchLink({
					url: `${BASE_URLS.CLIENT}/api/trpc`, // Our tRPC server
					headers: () => ({
						'content-type': 'application/json'
					})
				})
			]
		});

		// Create supertest instance for direct Identity service calls
		this.identityServiceRequest = supertest.default(BASE_URLS.IDP);
	}

	// Get the tRPC client
	static getTRPCClient() {
		if (!this.trpcClient) {
			this.initialize();
		}
		return this.trpcClient;
	}

	// Get direct Identity service request client
	static getIdentityServiceRequest() {
		if (!this.identityServiceRequest) {
			this.initialize();
		}
		return this.identityServiceRequest;
	}

	// Helper to create a test user directly in Identity service
	static async createTestUserInIdentityService(
		userPrefix: string = 'test',
		isAdmin: boolean = false
	): Promise<AuthResponse> {
		const userData = generateTestUserForRegistration(userPrefix);
		const request = this.getIdentityServiceRequest();

		try {
			console.log('Creating test user with data:', { ...userData, password: '[REDACTED]', isAdmin });
			const response = await request
				.post(`/${IDP_URI_CONSTANTS.AUTH.REGISTER}`)
				.send(userData)
				.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

			console.log('Registration response:', {
				status: response.status,
				body: response.body
			});

			// Extract tokens from the response structure
			const responseData = response.body.data;
			const tokens = responseData
				? {
						accessToken: responseData.token,
						refreshToken: responseData.refreshToken,
						expiresIn: 3600 // Default expiry
					}
				: undefined;

			// Extract user info from JWT token if available
			let user: AuthUser | undefined;
			if (tokens?.accessToken) {
				try {
					const decoded = jwtDecode<any>(tokens.accessToken);
					user = {
						id: decoded.userId,
						email: decoded.email,
						firstName: userData.firstName,
						lastName: userData.lastName,
						company: userData.company,
						clientOrigin: decoded.clientOrigin,
						roles: decoded.roles,
						isActive: decoded.isActive,
						emailVerified: false // Default for new registrations
					};
				} catch (error) {
					console.warn('Failed to decode JWT token:', error);
					user = { ...userData, id: 'unknown' };
				}
			} else {
				user = { ...userData, id: 'unknown' };
			}

			const authResponse: AuthResponse = {
				success: response.status === 201,
				user: user,
				tokens: tokens,
				response: response.body,
				status: response.status
			};

			// Store the user session if registration was successful
			if (authResponse.success && authResponse.user && authResponse.tokens) {
				const session: TestUserSession = {
					user: authResponse.user,
					tokens: authResponse.tokens,
					isAdmin: isAdmin
				};

				this.testUserSessions.set(authResponse.user.email, session);

				// Store admin token separately for cleanup operations
				if (isAdmin) {
					this.adminToken = authResponse.tokens.accessToken;
				}

				console.log(`✅ Stored ${isAdmin ? 'admin' : 'regular'} user session for: ${authResponse.user.email}`);
			}

			return authResponse;
		} catch (error: any) {
			console.error('Failed to create test user in Identity service:', {
				error: error.message,
				status: error.status,
				response: error.response?.body
			});
			return {
				success: false,
				user: { ...userData, id: 'unknown' },
				error: error instanceof Error ? error.message : 'Unknown error',
				status: error.status || 500
			};
		}
	}

	// Helper to login a user directly in Identity service
	static async loginUserInIdentityService(email: string, password: string): Promise<AuthResponse> {
		const request = this.getIdentityServiceRequest();

		try {
			const response = await request
				.post(`/${IDP_URI_CONSTANTS.AUTH.LOGIN}`)
				.send({ email, password })
				.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

			// Extract tokens from the response structure
			const responseData = response.body.data;
			const tokens = responseData
				? {
						accessToken: responseData.token,
						refreshToken: responseData.refreshToken,
						expiresIn: 3600 // Default expiry
					}
				: undefined;

			// Extract user info from JWT token if available
			let user: AuthUser | undefined;
			if (tokens?.accessToken) {
				try {
					const decoded = jwtDecode<any>(tokens.accessToken);
					user = {
						id: decoded.userId,
						email: decoded.email,
						firstName: decoded.firstName || 'Test',
						lastName: decoded.lastName || 'User',
						company: decoded.company || 'Test Company',
						clientOrigin: decoded.clientOrigin,
						roles: decoded.roles,
						isActive: decoded.isActive,
						emailVerified: true // Assume verified for login
					};
				} catch (error) {
					console.warn('Failed to decode JWT token:', error);
				}
			}

			const authResponse: AuthResponse = {
				success: response.status === 200,
				tokens: tokens,
				user: user,
				response: response.body,
				status: response.status
			};

			// Update stored session with new tokens if login was successful
			if (authResponse.success && authResponse.user && authResponse.tokens) {
				const existingSession = this.testUserSessions.get(email);
				if (existingSession) {
					existingSession.tokens = authResponse.tokens;
					existingSession.user = authResponse.user;
					console.log(`🔄 Updated stored session for: ${email}`);
				}
			}

			return authResponse;
		} catch (error: any) {
			console.error('Failed to login user in Identity service:', {
				error: error.message,
				status: error.status,
				response: error.response?.body
			});
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				status: error.status || 500
			};
		}
	}

	// Helper to check Identity service health directly
	static async checkIdentityServiceHealth() {
		const request = this.getIdentityServiceRequest();

		try {
			const response = await request
				.get(`/${IDP_URI_CONSTANTS.HEALTH.SHALLOW}`)
				.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

			return {
				success: response.status === 200,
				health: response.body.data,
				response: response.body,
				status: response.status
			};
		} catch (error) {
			console.error('Failed to check Identity service health:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				status: 500
			};
		}
	}

	// Helper to check Identity service deep health directly
	static async checkIdentityServiceDeepHealth() {
		const request = this.getIdentityServiceRequest();

		try {
			const response = await request
				.get(`/${IDP_URI_CONSTANTS.HEALTH.DEEP}`)
				.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

			return {
				success: [200, 503].includes(response.status), // 503 is acceptable for partial health
				health: response.body.data,
				response: response.body,
				status: response.status
			};
		} catch (error) {
			console.error('Failed to check Identity service deep health:', error);
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				status: 500
			};
		}
	}

	// Helper to get stored user session
	static getUserSession(email: string): TestUserSession | undefined {
		return this.testUserSessions.get(email);
	}

	// Helper to get admin token
	static getAdminToken(): string | null {
		return this.adminToken;
	}

	// Helper to create an admin user
	static async createAdminUser(userPrefix: string = 'admin'): Promise<AuthResponse> {
		return this.createTestUserInIdentityService(userPrefix, true);
	}

	// Helper to create a regular user
	static async createRegularUser(userPrefix: string = 'user'): Promise<AuthResponse> {
		return this.createTestUserInIdentityService(userPrefix, false);
	}

	// Helper to cleanup test users (requires admin access)
	static async cleanupTestUser(email: string, adminToken?: string) {
		const request = this.getIdentityServiceRequest();

		try {
			// Use provided admin token or stored admin token
			const token = adminToken || this.adminToken;
			const headers: Record<string, string> = {};

			if (token) {
				headers.authorization = `Bearer ${token}`;
				console.log(`🔑 Using admin token for cleanup: ${email}`);
			} else {
				console.warn(`⚠️ No admin token available for cleanup: ${email}`);
			}

			const response = await request
				.delete(`/${IDP_URI_CONSTANTS.ADMIN.DELETE_USER_BY_EMAIL(email)}`)
				.set(headers)
				.timeout(TEST_CONFIG.IDENTITY_SERVICE.TIMEOUT);

			// Remove from stored sessions
			this.testUserSessions.delete(email);

			return {
				success: [200, 204, 404].includes(response.status), // 404 is acceptable if user doesn't exist
				response: response.body,
				status: response.status
			};
		} catch (error: any) {
			console.error('Failed to cleanup test user:', {
				email,
				error: error.message,
				status: error.status,
				response: error.response?.body
			});
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error',
				status: error.status || 500
			};
		}
	}

	// Helper to cleanup all stored test users
	static async cleanupAllTestUsers(): Promise<void> {
		const adminToken = this.getAdminToken();
		if (!adminToken) {
			console.warn('⚠️ No admin token available for bulk cleanup');
			return;
		}

		const emails = Array.from(this.testUserSessions.keys());
		console.log(`🧹 Cleaning up ${emails.length} test users...`);

		for (const email of emails) {
			try {
				await this.cleanupTestUser(email, adminToken);
				console.log(`✅ Cleaned up user: ${email}`);
			} catch (error) {
				console.error(`❌ Failed to cleanup user ${email}:`, error);
			}
		}

		// Clear all stored sessions
		this.testUserSessions.clear();
		this.adminToken = null;
		console.log('🧹 All test user sessions cleared');
	}

	// Helper to get all stored user emails
	static getStoredUserEmails(): string[] {
		return Array.from(this.testUserSessions.keys());
	}

	// Helper to measure response time
	static measureResponseTime<T>(fn: () => Promise<T>): Promise<{ result: T; responseTime: number }> {
		const startTime = Date.now();
		return fn().then(result => ({
			result,
			responseTime: Date.now() - startTime
		}));
	}

	// Helper to wait for service to be ready
	static async waitForIdentityService(maxAttempts: number = 10, delayMs: number = 1000): Promise<boolean> {
		for (let attempt = 1; attempt <= maxAttempts; attempt++) {
			console.log(`Checking Identity service readiness (attempt ${attempt}/${maxAttempts})...`);

			const healthCheck = await this.checkIdentityServiceHealth();
			if (healthCheck.success) {
				console.log('✅ Identity service is ready');
				return true;
			}

			if (attempt < maxAttempts) {
				console.log(`❌ Identity service not ready, waiting ${delayMs}ms...`);
				await new Promise(resolve => setTimeout(resolve, delayMs));
			}
		}

		console.log('❌ Identity service failed to become ready');
		return false;
	}
}