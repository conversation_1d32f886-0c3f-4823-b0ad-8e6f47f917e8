# tRPC Integration Tests

This directory contains integration tests for the tRPC API layer that communicates with the Introspection.Identity microservice.

## Overview

These tests verify that the tRPC implementation correctly communicates with the actual Identity service, ensuring:
- Type safety across service boundaries
- Proper error handling and validation
- Performance requirements are met
- Service health monitoring works correctly

## Test Structure

```
tests/
├── config/
│   └── test.config.ts          # Test configuration and constants
├── helpers/
│   └── trpc-test.helper.ts     # Test utilities and helpers
├── integration/
│   ├── identity-health.test.ts      # Health check tests
│   ├── identity-login.test.ts       # Login functionality tests
│   └── identity-registration.test.ts # Registration functionality tests
├── setup.ts                    # Global test setup
├── run-integration-tests.ts    # Test runner script
└── README.md                   # This file
```

## Prerequisites

### 1. Identity Service Running
The Identity service must be running and accessible:
```bash
# Default location
http://localhost:3002

# Check if service is running
curl http://localhost:3002/api/v1/health
```

### 2. Environment Variables
Set up environment variables (optional):
```bash
export IDENTITY_SERVICE_URL=http://localhost:3002
export NODE_ENV=test
```

### 3. Dependencies
Ensure all test dependencies are installed:
```bash
bun install
```

## Running Tests

### Quick Start
```bash
# Run all integration tests
bun run test:integration

# Run specific test suites
bun run test:identity:health
bun run test:identity:login
bun run test:identity:registration
```

### Comprehensive Test Runner
```bash
# Run with detailed reporting
bun run tests/run-integration-tests.ts
```

### Individual Test Files
```bash
# Run specific test file
bun test tests/integration/identity-login.test.ts

# Run with watch mode
bun test --watch tests/integration/identity-health.test.ts
```

## Test Categories

### 1. Health Check Tests (`identity-health.test.ts`)
- **Basic Health Check**: Verifies service responds with healthy status
- **Response Structure**: Validates health check response format
- **Performance**: Ensures health checks complete within time limits
- **Load Testing**: Tests health checks under concurrent load
- **Deep Health Check**: Tests comprehensive health endpoint (`/health/all`)

**Key Assertions:**
- Service returns `status: 'healthy'`
- Response time < 1000ms
- Consistent response structure
- Handles concurrent requests

### 2. Registration Tests (`identity-registration.test.ts`)
- **Successful Registration**: Valid user creation
- **Duplicate Email**: Prevents duplicate registrations
- **Input Validation**: Email format, password strength, required fields
- **Special Characters**: Handles international names and characters
- **Performance**: Registration completes within time limits
- **Concurrent Registration**: Multiple simultaneous registrations

**Key Assertions:**
- Valid users are created successfully
- Duplicate emails are rejected
- Input validation works correctly
- Response time < 5000ms
- Consistent response structure

### 3. Login Tests (`identity-login.test.ts`)
- **Successful Login**: Valid credentials authentication
- **Invalid Credentials**: Wrong password/email rejection
- **Input Validation**: Email format validation
- **Remember Me**: Persistent session handling
- **Performance**: Login completes within time limits
- **Concurrent Login**: Multiple simultaneous logins

**Key Assertions:**
- Valid credentials return tokens
- Invalid credentials are rejected
- JWT tokens are properly formatted
- Response time < 3000ms
- Consistent response structure

## Test Configuration

### Performance Thresholds
```typescript
PERFORMANCE: {
  HEALTH_CHECK_MAX_TIME: 1000,    // 1 second
  LOGIN_MAX_TIME: 3000,           // 3 seconds
  REGISTRATION_MAX_TIME: 5000,    // 5 seconds
}
```

### Test Data
```typescript
TEST_DATA: {
  CLIENT_ORIGIN: 1,                    // EnumClientOrigin.Excelytics
  VALID_PASSWORD: 'TestPassword@123',
  VALID_EMAIL_DOMAIN: '@excelytics.com',
}
```

## Test Utilities

### TRPCTestHelper
Main helper class providing:
- tRPC client initialization
- Direct Identity service communication
- Test user creation and cleanup
- Performance measurement
- Service health checking

### Key Methods
```typescript
// Create test user in Identity service
await TRPCTestHelper.createTestUserInIdentityService('test-prefix');

// Login user directly
await TRPCTestHelper.loginUserInIdentityService(email, password);

// Check service health
await TRPCTestHelper.checkIdentityServiceHealth();

// Measure response time
await TRPCTestHelper.measureResponseTime(async () => {
  return await someAsyncOperation();
});

// Wait for service to be ready
await TRPCTestHelper.waitForIdentityService();
```

## Expected Test Results

### Successful Run
```
🚀 Starting tRPC Integration Test Suite
============================================================

🔍 Checking Identity service availability...
✅ Identity service is available for testing

🧪 Running Health Checks Tests
----------------------------------------
✅ Health Checks: 8 tests passed in 1247ms

🧪 Running User Registration Tests
----------------------------------------
✅ User Registration: 12 tests passed in 3891ms

🧪 Running User Login Tests
----------------------------------------
✅ User Login: 10 tests passed in 2156ms

============================================================
📊 INTEGRATION TEST REPORT
============================================================

📈 Summary:
   Total Tests: 30
   Passed: 30 ✅
   Failed: 0 ✅
   Duration: 7294ms
   Success Rate: 100.0%
```

## Troubleshooting

### Common Issues

1. **Identity Service Not Running**
   ```
   ❌ Cannot run integration tests - Identity service is not available
   ```
   **Solution**: Start the Identity service on `http://localhost:3002`

2. **Connection Refused**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3002
   ```
   **Solution**: Verify Identity service is running and accessible

3. **Test Timeouts**
   ```
   Error: Test timeout exceeded
   ```
   **Solution**: Check service performance or increase timeout values

4. **Authentication Errors**
   ```
   Error: Unauthorized
   ```
   **Solution**: Verify test user creation and credentials

### Debugging Tips

1. **Enable Verbose Logging**
   ```bash
   DEBUG=* bun test tests/integration/identity-login.test.ts
   ```

2. **Check Service Logs**
   Monitor Identity service logs during test execution

3. **Verify Service Health**
   ```bash
   curl -v http://localhost:3002/api/v1/health
   curl -v http://localhost:3002/api/v1/health/all
   ```

4. **Test Individual Components**
   ```bash
   # Test just health checks
   bun run test:identity:health
   
   # Test just registration
   bun run test:identity:registration
   ```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    services:
      identity:
        image: introspection/identity:latest
        ports:
          - 3002:3002
        env:
          NODE_ENV: test
    
    steps:
      - uses: actions/checkout@v3
      - uses: oven-sh/setup-bun@v1
      - run: bun install
      - run: bun run test:integration
```

## Performance Monitoring

The tests include performance monitoring to ensure:
- Health checks complete within 1 second
- Login operations complete within 3 seconds
- Registration operations complete within 5 seconds
- Concurrent operations maintain acceptable performance

Performance data is logged and can be used for:
- Service optimization
- Performance regression detection
- Capacity planning

## Contributing

When adding new integration tests:

1. **Follow Naming Convention**: `identity-[feature].test.ts`
2. **Include Performance Tests**: Measure and assert response times
3. **Test Error Cases**: Verify proper error handling
4. **Clean Up Resources**: Remove test data after tests
5. **Document Test Purpose**: Clear descriptions and comments
6. **Update This README**: Document new test categories

## Security Considerations

- Test users are automatically cleaned up after tests
- Passwords use secure test patterns
- No production data is used in tests
- Test tokens are not logged or persisted

---

For questions or issues with the integration tests, check:
1. Identity service status and logs
2. Network connectivity
3. Test configuration in `test.config.ts`
4. This README for troubleshooting steps
