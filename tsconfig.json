// ROOT tsconfig
{
	"compilerOptions": {
		"lib": ["ESNext"],
		"module": "ESNext",
		"target": "ESNext",
		"types": ["bun-types"],
		"moduleResolution": "bundler",
		"paths": {
			"@/*": ["./*"]
		},

		"noEmit": true,
		"strict": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"forceConsistentCasingInFileNames": true
	},
	"include": ["tests/**/*.ts", "shared/**/*.ts"],
	"exclude": ["node_modules", "**/dist", "**/build"]
}