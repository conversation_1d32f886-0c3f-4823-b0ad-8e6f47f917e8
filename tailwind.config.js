/** @type {import('tailwindcss').Config} */
module.exports = {
	darkMode: ["class"],
	content: ["./client/public/index.html", "./client/src/**/*.{js,ts,jsx,tsx}"],
	safelist: [
		// Colors
		'bg-primary', 'bg-secondary', 'bg-chart-1', 'bg-chart-2', 'bg-chart-3', 'bg-chart-4', 'bg-chart-5',
		'bg-red-500', 'bg-green-500', 'bg-blue-500', 'bg-blue-600', 'bg-blue-700',
		'text-white', 'text-gray-500', 'text-gray-600', 'text-gray-700', 'text-gray-900',
		// Typography
		'text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'font-bold', 'font-semibold',
		// Layout
		'p-4', 'p-8', 'py-2', 'px-4', 'space-y-2', 'space-y-4', 'space-x-2',
		'rounded', 'rounded-lg', 'rounded-xl', 'rounded-full',
		'grid', 'grid-cols-3', 'gap-4', 'flex', 'items-center', 'justify-between',
		'w-full', 'max-w-md', 'mx-auto', 'h-16', 'w-4', 'h-4', 'w-8', 'h-8',
		// Effects
		'shadow-lg', 'hover:bg-blue-700', 'transition-colors',
		'border-2', 'border-dashed', 'border-gray-300',
		'bg-gradient-to-r', 'from-blue-500', 'to-purple-600',
		// Responsive
		'md:w-12', 'md:h-12', 'lg:w-16', 'lg:h-16'
	],
	theme: {
		extend: {
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			colors: {
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				chart: {
					'1': 'hsl(var(--chart-1))',
					'2': 'hsl(var(--chart-2))',
					'3': 'hsl(var(--chart-3))',
					'4': 'hsl(var(--chart-4))',
					'5': 'hsl(var(--chart-5))'
				}
			}
		}
	},
	plugins: [require('tailwindcss-animate')],
};