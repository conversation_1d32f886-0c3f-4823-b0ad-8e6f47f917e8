# Temporarily using public registry due to Azure DevOps 2GB storage limit
registry=https://registry.npmjs.org/

# Azure DevOps configuration (restore after cleaning up storage)
# registry=https://pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/
# always-auth=true
# //pkgs.dev.azure.com/introspectiondev/983c1cd1-9f14-43d7-9675-4f43eb148d92/_packaging/FinanceFeed/npm/registry/:_authToken=${ADO_PAT}