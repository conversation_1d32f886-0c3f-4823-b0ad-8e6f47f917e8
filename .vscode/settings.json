{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/css_custom_data.json"], "stylelint.enable": true, "stylelint.validate": ["css", "postcss"], "editor.codeActionsOnSave": {"source.fixAll.stylelint": "explicit"}, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}