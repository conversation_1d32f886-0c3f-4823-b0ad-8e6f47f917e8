#--------------------------------------------------------------------------
# Introspection.Client - Environment Configuration
#--------------------------------------------------------------------------

# --- MongoDB Credentials (safer) ---
MONGODB_URI=transformed-by-env-file
MONGO_USER=admin
MONGO_PASS="u?yNxEWg#3"
MONGO_HOST=************
MONGO_PORT=27017
MONGO_DB_NAME=introspection
MONGO_AUTH_SOURCE=admin&replicaSet=rs0

# MongoDB URIs
MONGODB_URI_UNRAID=********************************************************************************
MONGODB_URI_LOCAL=mongodb://localhost:27017/introspection
MONGODB_URI_STAGNG=NONE
MONGODB_URI_PROD=NONE

# Service Identification
SERVICE_NAME=excelytics.client

# IP Addresses & Ports
REDIS_URI_UNRAID=************
REDIS_HOST=************
TAILSCALE_IP=************
REDIS_PORT=6379
ENV=development
PORT=4200

# --- Microservices URLs (Revised Ports) ---
IDP_SERVICE_URL=http://localhost:6002
CALC_SERVICE_URL=http://localhost:6003
CLIENT_SERVICE_URL=http://localhost:4200
FINANCE_SERVICE_URL=http://localhost:6001
API_GATEWAY_URL=none

# Domains / Allowed Redirect URIs (FE)
# These are crucial for OAuth/OIDC flows to know where to redirect users. Often, allowed redirect URIs are stored per client in the database
IDB_BASE_URL=http://auth.excelytics.co.za
CLIENT_APP_LOGOUT_SUCCESS_REDIRECT_URI=https://app.exelytics.co.za/login
CLIENT_APP_LOGIN_SUCCESS_REDIRECT_URI=https://app.exelytics.co.za/dashboard

# Miscellaneous
VPN=true
VERSION=1.1.2
API_VERSION=v1

# Log Levels: (TRACE, DEBUG, INFO, WARN, ERROR, SILENT)
LOG_LEVEL=DEBUG

# --- Error Handling & Logging Configuration (may not be needed here) ---
# Enable verbose error logging (overrides environment-based defaults)
DEBUG_ERRORS=false

# Enable logging of sensitive information (development only)
# WARNING: Only enable in development - never in production
LOG_SENSITIVE=false

# Enable verbose logging in tests (overrides environment-based defaults)
TEST_VERBOSE_LOGGING=false