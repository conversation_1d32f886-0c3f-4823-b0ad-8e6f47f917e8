{"name": "introspection.finance.client", "description": "Monorepo for Introspection.Finance.Client", "version": "1.1.2", "type": "module", "author": "<PERSON><PERSON>", "private": true, "scripts": {"dev": "vite", "build": "vite build", "build:prod": "NODE_ENV=production vite build", "preview": "vite preview", "serve": "bun run server/config/server.ts", "start": "bun run serve", "check:types:client": "cd client && tsc --noEmit", "check:types:server": "cd server && tsc --noEmit", "check:types:tests": "cd tests && tsc --noEmit", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write . && bun run format:EOF", "format:EOF": "bun run scripts/eof-remover.script.ts", "format:EOF:dry": "bun run scripts/eof-remover.script.ts --dry-run", "lint": "eslint .", "lint:errors-only": "eslint . --quiet", "lint:fix": "eslint . --fix", "lint:css": "stylelint \"**/*.css\"", "lint:css:fix": "stylelint \"**/*.css\" --fix", "reinstall": "bun run scripts/cli.ts reinstall", "clean:imports": "bun run scripts/cli.ts clean:imports", "map:folders": "bun run scripts/cli.ts map --folders-only", "map:enhanced": "bun run scripts/cli.ts map:enhanced --show-all-with-hide-list", "clean": "rm -rf client/dist", "clean:all": "rm -rf node_modules client/dist", "test:idp-direct:admin-auth": "bun test tests/integration/identity-direct-api/admin-auth.test.ts", "test:idp-direct:auth-types": "bun test tests/integration/identity-direct-api/auth-types.test.ts", "test:idp-direct:login": "bun test tests/integration/identity-direct-api/login.test.ts", "test:idp-direct:registration": "bun test tests/integration/identity-direct-api/registration.test.ts", "test:idp-trpc:admin-auth": "bun test tests/integration/identity-trpc/admin-auth.test.ts", "test:idp-trpc:auth-types": "bun test tests/integration/identity-trpc/auth-types.test.ts", "test:idp-trpc:login": "bun test tests/integration/identity-trpc/login.test.ts", "test:idp-trpc:registration": "bun test tests/integration/identity-trpc/registration.test.ts"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.84.1", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@types/compression": "^1.8.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "cosmiconfig": "^9.0.0", "deepmerge": "^4.3.1", "exceljs": "^4.4.0", "@excelytics/shared-dtos": "0.1.9", "@excelytics/shared-models": "^0.2.18", "@excelytics/utility-scripts": "^0.0.2", "@types/supertest": "^6.0.3", "ioredis": "^5.6.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.536.0", "mongoose": "^8.8.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "recharts": "2.15.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/bun": "latest", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/helmet": "^0.0.48", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "bun": "^1.2.19", "bun-types": "^1.2.18", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-formatter-codeframe": "^7.32.1", "eslint-formatter-compact": "^8.40.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^15.12.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "stylelint": "^16.22.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "supertest": "^7.0.0", "tailwindcss": "^3.4.0", "tsconfig-paths": "^4.2.0", "typescript": "5.8", "typescript-eslint": "^8.16.0", "vite": "^7.1.1"}}