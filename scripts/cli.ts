import { cli_entry } from 'excelytics.utility-scripts';

/**
 * CLI dispatcher. The first argument determines which script to run.
 * Loads configuration once and passes it to individual functions.
 * Commands:
 *  - map:enhanced
 *  - map
 *  - reinstall
 *  - clean:imports
 *  - config:test
 */
if (import.meta.main) {
	cli_entry().catch(err => {
		console.error('<PERSON><PERSON><PERSON> failed to execute:', err);
		process.exit(1);
	});
}