import { readdirSync, statSync, readFileSync, writeFileSync } from 'fs';
import { join, extname } from 'path';

interface Stats {
	filesProcessed: number;
	filesModified: number;
	errors: number;
}

const stats: Stats = {
	filesProcessed: 0,
	filesModified: 0,
	errors: 0
};

const TARGET_EXTENSIONS = ['.ts', '.tsx', '.json', '.jsonc', '.yml', '.yaml'];
const IGNORE_PATTERNS = ['node_modules', 'dist', 'build', '.git', '.next', 'coverage', '.nyc_output'];

// Configuration options (ensure false)
const KEEP_ONE_TRAILING_NEWLINE = false;

function shouldIgnoreDirectory(dirName: string): boolean {
	return IGNORE_PATTERNS.some(pattern => dirName.includes(pattern));
}

function shouldProcessFile(filePath: string): boolean {
	const ext = extname(filePath);
	return TARGET_EXTENSIONS.includes(ext);
}

function removeTrailingEmptyLines(content: string): { modified: string; changed: boolean } {
	// Handle empty or whitespace-only files
	if (!content || content.trim() === '') {
		const modified = KEEP_ONE_TRAILING_NEWLINE ? '\n' : '';
		return { modified, changed: content !== modified };
	}

	// Split content into lines, preserving original line endings
	const hasWindowsLineEndings = content.includes('\r\n');
	const lineEnding = hasWindowsLineEndings ? '\r\n' : '\n';
	const lines = content.split(lineEnding);

	// Find the last non-empty line (from the end)
	let lastContentIndex = -1;
	for (let i = lines.length - 1; i >= 0; i--) {
		if (lines[i]!.trim() !== '') {
			lastContentIndex = i;
			break;
		}
	}

	// If no actual content lines (only whitespace/empty lines existed),
	// return a single newline or empty string based on config.
	if (lastContentIndex === -1) {
		const modified = KEEP_ONE_TRAILING_NEWLINE ? lineEnding : '';
		return { modified, changed: content !== modified };
	}

	// Construct the new content based on KEEP_ONE_TRAILING_NEWLINE
	let targetLines: string[] = lines.slice(0, lastContentIndex + 1);
	if (KEEP_ONE_TRAILING_NEWLINE) {
		targetLines.push('');
	}

	const modifiedContent = targetLines.join(lineEnding);

	const changed = modifiedContent !== content;

	return { modified: modifiedContent, changed };
}

function processFile(filePath: string, isDryRun: boolean): void {
	try {
		stats.filesProcessed++;

		const originalContent = readFileSync(filePath, 'utf-8');
		const { modified, changed } = removeTrailingEmptyLines(originalContent);

		if (changed) {
			if (!isDryRun) {
				writeFileSync(filePath, modified, 'utf-8');
				stats.filesModified++;
				const originalLines = originalContent.split(/\r?\n/).length;
				const modifiedLines = modified.split(/\r?\n/).length;
				console.log(`✅ Fixed: ${filePath} (${originalLines} -> ${modifiedLines} lines)`);
			} else {
				// Count as modified in dry run for summary
				stats.filesModified++;
				console.log(`🔍 Would fix: ${filePath}`);
			}
		} else {
			console.log(`⚪ No change: ${filePath}`);
		}
	} catch (error) {
		stats.errors++;
		console.error(`❌ Error processing ${filePath}:`, error);
	}
}

function walkDirectory(dirPath: string, isDryRun: boolean): void {
	try {
		const entries = readdirSync(dirPath);

		for (const entry of entries) {
			const fullPath = join(dirPath, entry);

			// Defensive check: ensure file/directory still exists
			try {
				const stat = statSync(fullPath);

				if (stat.isDirectory()) {
					if (!shouldIgnoreDirectory(entry)) {
						walkDirectory(fullPath, isDryRun);
					}
				} else if (stat.isFile() && shouldProcessFile(fullPath)) {
					processFile(fullPath, isDryRun);
				}
			} catch (statError: any) {
				// If file or directory vanished during scan, log and continue
				if (statError.code === 'ENOENT') {
					console.log(`⚠️  Skipping ${fullPath} (not found)`);
				} else {
					throw statError;
				}
			}
		}
	} catch (error) {
		stats.errors++;
		console.error(`❌ Error reading directory ${dirPath}:`, error);
	}
}

function main(): void {
	const rawArgs = process.argv.slice(2); // Get arguments after 'bun run script.ts'

	let startPath: string = '.';
	let verbose: boolean = false; // Not fully implemented yet, but good to have
	let dryRun: boolean = false;

	// Parse arguments: separate flags from the path
	for (const arg of rawArgs) {
		if (arg === '--verbose' || arg === '-v') {
			verbose = true;
		} else if (arg === '--dry-run' || arg === '-n') {
			dryRun = true;
		} else {
			// Assume any non-flag argument is the startPath.
			// If multiple non-flag args are passed, only the last one will be used.
			startPath = arg;
		}
	}

	console.log('🧹 Removing trailing empty lines from files...');
	console.log(`📂 Starting from: ${startPath}`);
	console.log(`🎯 Target extensions: ${TARGET_EXTENSIONS.join(', ')}`);
	console.log(`🚫 Ignoring: ${IGNORE_PATTERNS.join(', ')}`);
	console.log(`📝 Keep one trailing newline: ${KEEP_ONE_TRAILING_NEWLINE}`);
	if (dryRun) console.log('🔍 DRY RUN MODE - No files will be modified');
	console.log('');

	const startTime = Date.now();

	try {
		const startStat = statSync(startPath);

		if (startStat.isFile()) {
			// Process a single file
			if (shouldProcessFile(startPath)) {
				processFile(startPath, dryRun);
			} else {
				console.log(`⚠️  File ${startPath} is not a target file type. Skipping.`);
			}
		} else if (startStat.isDirectory()) {
			// Process directory recursively
			walkDirectory(startPath, dryRun);
		} else {
			console.error(`❌ Path is neither a file nor a directory: ${startPath}`);
			process.exit(1);
		}
	} catch (error: any) {
		if (error.code === 'ENOENT') {
			console.error(`❌ Error: The specified path does not exist: ${startPath}`);
		} else {
			console.error(`❌ An unexpected error occurred: ${error.message}`);
		}
		process.exit(1);
	}

	const endTime = Date.now();
	const duration = endTime - startTime;

	console.log('');
	console.log('📊 Summary:');
	console.log(`   Files processed: ${stats.filesProcessed}`);
	console.log(`   Files modified (or would be): ${stats.filesModified}`);
	console.log(`   Errors: ${stats.errors}`);
	console.log(`   Duration: ${duration}ms`);

	if (stats.errors > 0) {
		console.log('\n❌ Script completed with errors');
		process.exit(1);
	} else if (stats.filesModified > 0) {
		console.log('\n✅ Script completed successfully');
	} else {
		console.log('\n✨ No changes needed');
	}
}

// Ensures main() is called only when the script is run directly
if (import.meta.main) {
	main();
}