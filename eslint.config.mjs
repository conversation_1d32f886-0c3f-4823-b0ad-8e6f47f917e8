import reactHooksPlugin from 'eslint-plugin-react-hooks';
import perfectionist from 'eslint-plugin-perfectionist';
import eslintPluginImport from 'eslint-plugin-import';
import reactPlugin from 'eslint-plugin-react';
import pluginNode from 'eslint-plugin-node';
import tseslint from 'typescript-eslint';
import pluginJs from '@eslint/js';
import globals from 'globals';

/** @type {import('eslint').Linter.Config[]} */
export default [
	// A dedicated, top-level ignores configuration.
	{
		ignores: [
			'docs/**',
			'logs/**',
			'types/**',
			'scripts/**',
			'.eslintcache',
			'client/dist/**',
			'server/dist/**',
			'node_modules/**'
		]
	},

	// CLIENT (React/Frontend) files
	{
		files: ['client/src/**/*.{ts,tsx}'],
		languageOptions: {
			globals: { ...globals.browser, React: 'readonly' },
			parser: tseslint.parser,
			parserOptions: {
				project: './client/tsconfig.json',
				ecmaFeatures: { jsx: true }
			}
		},
		plugins: {
			'@typescript-eslint': tseslint.plugin,
			import: eslintPluginImport,
			perfectionist,
			react: reactPlugin,
			'react-hooks': reactHooksPlugin
		},
		settings: {
			react: { version: 'detect' },
			'import/resolver': {
				typescript: {
					project: './client/tsconfig.json',
					alwaysTryTypes: true
				}
			}
		},
		rules: {
			...pluginJs.configs.recommended.rules,
			...tseslint.configs.recommended.rules,
			...reactPlugin.configs.recommended.rules,
			...reactHooksPlugin.configs.recommended.rules,

			// React-specific rules
			'react/react-in-jsx-scope': 'off', // Not needed with React 17+ JSX transform
			'react/jsx-uses-react': 'off', // Not needed with React 17+ JSX transform
			'react/prop-types': 'off', // Using TypeScript for prop validation

			// Initial Rules (from IdP config)
			'no-mixed-spaces-and-tabs': 'error',
			'@typescript-eslint/no-explicit-any': 'off',
			// Disable indent rule for React files due to JSX stack overflow issue
			// 'indent': ['error', 'tab', { SwitchCase: 1 }],
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all',
					args: 'after-used',
					argsIgnorePattern: '^_',
					ignoreRestSiblings: true
				}
			],
			'no-warning-comments': [
				'warn',
				{
					terms: ['todo', 'fixme'],
					location: 'start'
				}
			],

			// --- 'sort-imports' Rule for Perfectionist ---
			'perfectionist/sort-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
					groups: [['import']],
					newlinesBetween: 'always'
				}
			],
			// Sort the items inside the curly braces by length
			'perfectionist/sort-named-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc'
				}
			],

			// --- Keep these non-conflicting rules from eslint-plugin-import ---
			'import/order': 'off', // MUST be off to prevent conflicts
			'import/newline-after-import': ['error', { count: 1 }],
			'import/no-duplicates': 'error',
			'import/extensions': ['off', 'ignorePackages', { ts: 'never', tsx: 'never' }],

			'no-unused-vars': 'off',

			// --- Other Quality Rules ---
			'prefer-const': 'error',
			'no-var': 'error',
			'no-console': 'off',
			'no-debugger': 'error',
			'no-unused-expressions': 'warn'
		}
	},

	// SERVER (Node.js/Backend) files
	{
		files: ['server/**/*.{js,mjs,cjs,ts}'],
		languageOptions: {
			globals: { ...globals.node, Bun: 'readonly' },
			parser: tseslint.parser,
			parserOptions: {
				project: './server/tsconfig.json'
			}
		},
		plugins: {
			'@typescript-eslint': tseslint.plugin,
			import: eslintPluginImport,
			perfectionist,
			node: pluginNode
		},
		rules: {
			// Base configurations
			...pluginJs.configs.recommended.rules,
			...tseslint.configs.recommended.rules,

			// Initial Rules (from IdP config)
			'node/no-process-env': 'off',
			'no-mixed-spaces-and-tabs': 'error',
			'@typescript-eslint/no-explicit-any': 'off',
			'indent': ['error', 'tab', { SwitchCase: 1 }],
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all',
					args: 'after-used',
					argsIgnorePattern: '^_',
					ignoreRestSiblings: true
				}
			],
			'no-warning-comments': [
				'warn',
				{
					terms: ['todo', 'fixme'],
					location: 'start'
				}
			],

			'no-unused-vars': 'off',

			// --- 'sort-imports' Rule for Perfectionist ---
			'perfectionist/sort-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
					groups: [['import']],
					newlinesBetween: 'always'
				}
			],
			// Sort the items inside the curly braces by length
			'perfectionist/sort-named-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc'
				}
			],

			// --- Keep these non-conflicting rules from eslint-plugin-import ---
			'import/order': 'off', // MUST be off to prevent conflicts
			'import/newline-after-import': ['error', { count: 1 }],
			'import/no-duplicates': 'error',
			'import/extensions': ['off', 'ignorePackages', { ts: 'never' }],

			// --- Other Quality Rules ---
			'prefer-const': 'error',
			'no-var': 'error',
			'no-console': 'off',
			'no-debugger': 'error',
			'no-unused-expressions': 'warn'
		},
		settings: {
			'import/resolver': {
				typescript: {
					project: './server/tsconfig.json',
					alwaysTryTypes: true
				}
			}
		}
	},

	// SHARED files
	{
		files: ['shared/**/*.{js,mjs,cjs,ts}'],
		languageOptions: {
			globals: { ...globals.node, Bun: 'readonly' },
			parser: tseslint.parser,
			parserOptions: {
				project: './shared/tsconfig.json'
			}
		},
		plugins: {
			'@typescript-eslint': tseslint.plugin,
			import: eslintPluginImport,
			perfectionist
		},
		rules: {
			// Base configurations
			...pluginJs.configs.recommended.rules,
			...tseslint.configs.recommended.rules,

			// Basic rules
			'no-mixed-spaces-and-tabs': 'error',
			'@typescript-eslint/no-explicit-any': 'off',
			'indent': ['error', 'tab', { SwitchCase: 1 }],
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all',
					args: 'after-used',
					argsIgnorePattern: '^_',
					ignoreRestSiblings: true
				}
			],
			'no-warning-comments': [
				'warn',
				{
					terms: ['todo', 'fixme'],
					location: 'start'
				}
			],

			'no-unused-vars': 'off',

			// Import sorting
			'perfectionist/sort-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc',
					groups: [['import']],
					newlinesBetween: 'always'
				}
			],
			'perfectionist/sort-named-imports': [
				'error',
				{
					type: 'line-length',
					order: 'desc'
				}
			],

			'import/order': 'off',
			'import/newline-after-import': ['error', { count: 1 }],
			'import/no-duplicates': 'error',
			'import/extensions': ['off', 'ignorePackages', { ts: 'never' }],

			// Quality rules
			'prefer-const': 'error',
			'no-var': 'error',
			'no-console': 'off',
			'no-debugger': 'error',
			'no-unused-expressions': 'warn'
		},
		settings: {
			'import/resolver': {
				typescript: {
					project: './shared/tsconfig.json',
					alwaysTryTypes: true
				}
			}
		}
	}
];