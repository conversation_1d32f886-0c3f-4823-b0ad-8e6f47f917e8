# ESLint Code Quality Check for Client and Server
steps:
  - script: |
      echo "🔍 Running ESLint on client and server directories..."
      echo "Current directory: $(pwd)"
      echo "Node version: $(node --version)"
      echo "Bun version: $(bun --version)"
    displayName: 'Lint - Environment Info'

  - script: |
      echo "📋 ESLint Configuration Check..."
      if [ -f "eslint.config.mjs" ]; then
        echo "✅ ESLint config found: eslint.config.mjs"
        echo "📄 Config preview:"
        head -20 eslint.config.mjs
      else
        echo "❌ ESLint config not found!"
        exit 1
      fi
    displayName: 'Lint - Verify ESLint Config'

  - script: |
      echo "🔧 Running ESLint with error-only output..."
      echo "Linting client, server, and shared directories..."

      # Run ESLint and capture output
      if bun run lint:errors-only > eslint-results.txt 2>&1; then
        echo "✅ ESLint passed with no errors!"
        echo "📊 ESLint Results:"
        cat eslint-results.txt
        
        # Count warnings if any
        WARNING_COUNT=$(grep -c "warning" eslint-results.txt || echo "0")
        echo "⚠️  Total warnings: $WARNING_COUNT"
        
        # Create success summary
        cat > eslint-summary.md << EOF
      # 🎯 ESLint Results - PASSED

      ## ✅ Code Quality Check
      - **Status:** PASSED ✅
      - **Errors:** 0
      - **Warnings:** $WARNING_COUNT
      - **Directories Checked:** client/, server/, shared/
      - **Rules:** Perfectionist import sorting, TypeScript, React

      ## 📋 Configuration
      - **Config File:** eslint.config.mjs
      - **TypeScript Version:** $(bunx tsc --version)
      - **ESLint Plugins:** perfectionist, typescript-eslint, react

      EOF
        
      else
        echo "❌ ESLint found errors!"
        echo "📊 ESLint Results:"
        cat eslint-results.txt
        
        # Count errors and warnings
        ERROR_COUNT=$(grep -c "error" eslint-results.txt || echo "0")
        WARNING_COUNT=$(grep -c "warning" eslint-results.txt || echo "0")
        
        echo "🚨 Total errors: $ERROR_COUNT"
        echo "⚠️  Total warnings: $WARNING_COUNT"
        
        # Create failure summary
        cat > eslint-summary.md << EOF
      # 🚨 ESLint Results - FAILED

      ## ❌ Code Quality Issues Found
      - **Status:** FAILED ❌
      - **Errors:** $ERROR_COUNT
      - **Warnings:** $WARNING_COUNT
      - **Directories Checked:** client/, server/, shared/

      ## 📋 Issues to Fix
      \`\`\`
      $(cat eslint-results.txt)
      \`\`\`

      ## 🔧 How to Fix
      1. Run \`bun run lint\` locally to see all issues
      2. Run \`bun run lint:fix\` to auto-fix import sorting
      3. Manually fix remaining errors
      4. Commit and push changes

      EOF
        
        exit 1
      fi
    displayName: 'Lint - Run ESLint Check'
    continueOnError: false

  - script: |
      echo "📊 Generating detailed lint report..."

      # Run full lint to get complete report (including warnings)
      echo "Running full ESLint report..."
      bun run lint > eslint-full-report.txt 2>&1 || true

      # Extract statistics
      TOTAL_FILES=$(find client server shared -name "*.ts" -o -name "*.tsx" | wc -l)
      CLIENT_FILES=$(find client -name "*.ts" -o -name "*.tsx" | wc -l)
      SERVER_FILES=$(find server -name "*.ts" | wc -l)
      SHARED_FILES=$(find shared -name "*.ts" | wc -l)

      echo "📈 Lint Statistics:"
      echo "- Total TypeScript files: $TOTAL_FILES"
      echo "- Client files: $CLIENT_FILES"
      echo "- Server files: $SERVER_FILES"
      echo "- Shared files: $SHARED_FILES"

      # Append statistics to summary
      cat >> eslint-summary.md << EOF

      ## 📈 Project Statistics
      - **Total TypeScript Files:** $TOTAL_FILES
      - **Client Files (.ts/.tsx):** $CLIENT_FILES
      - **Server Files (.ts):** $SERVER_FILES
      - **Shared Files (.ts):** $SHARED_FILES

      ## 🔧 ESLint Rules Active
      - **Import Sorting:** perfectionist/sort-imports
      - **Named Import Sorting:** perfectionist/sort-named-imports
      - **TypeScript Rules:** @typescript-eslint/*
      - **React Rules:** react/* (client only)
      - **Code Quality:** prefer-const, no-unused-vars, etc.

      EOF

    displayName: 'Lint - Generate Statistics'
    condition: always()

  - task: PublishTestResults@2
    displayName: 'Lint - Publish Results as Test Results'
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: 'eslint-results.xml'
      failTaskOnFailedTests: true
      testRunTitle: 'ESLint Code Quality'
    condition: always()
    continueOnError: true

  - script: |
      echo "📄 ESLint Summary:"
      if [ -f "eslint-summary.md" ]; then
        cat eslint-summary.md
      fi

      echo ""
      echo "📁 Generated Files:"
      ls -la eslint-* || echo "No eslint files generated"
    displayName: 'Lint - Display Summary'
    condition: always()