# This template installs Client (FE) microservice dependencies using Bun.
# Prerequisites: Node.js, Bun, and Azure Artifacts authentication should be handled in setup-environment.yaml
steps:
  - script: |
      echo "Installing FE microservice dependencies using Bun..."
      echo "Current directory: $(pwd)"
      echo "FE microservice contents:"
      ls -la
      echo "Source folder contents:"
      ls -la src/ || echo "Source folder not found"
      echo "Tests folder contents:"
      ls -la tests/ || echo "Tests folder not found"

      # Show authentication status
      echo "Checking authentication setup..."
      if [ -f ".npmrc" ]; then
        echo "✅ Root .npmrc exists"
        grep "registry=" .npmrc || echo "No registry found"
        grep "always-auth" .npmrc || echo "No always-auth found"
      else
        echo "❌ Root .npmrc not found"
        exit 1
      fi

      if [ -f "$HOME/.npmrc" ]; then
        echo "✅ Home .npmrc exists"
      else
        echo "❌ Home .npmrc not found"
        exit 1
      fi

      rm -f bun.lock
      echo "Installing FE microservice dependencies..."
      bun install
      echo "✅ FE microservice dependencies installed successfully"

      # Verify key dependencies are installed
      echo "Verifying critical FE dependencies..."
      if [ -d "node_modules/mongoose" ]; then
        echo "✅ Mongoose ODM installed"
      else
        echo "❌ Mongoose ODM missing"
        exit 1
      fi

      echo "✅ All critical FE dependencies verified"
    displayName: 'Install FE Microservice Dependencies (Bun)'