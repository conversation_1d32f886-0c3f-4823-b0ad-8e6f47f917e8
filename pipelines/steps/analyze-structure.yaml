# Generate project structure documentation and set pipeline variables for FE microservice
steps:
  # Generate the file structure map using map:enhanced script
  - script: |
      echo "Generating enhanced FE project structure analysis..."
      set -e
      bun run map:enhanced
      echo "✅ FE structure analysis completed."
    displayName: 'Generate Enhanced FE Structure Map'
    workingDirectory: '$(System.DefaultWorkingDirectory)'

  - script: |
      echo "Looking for generated FE structure files..."
      EXPECTED_FILENAME_PATTERN="*_enhanced_structure.md"

      STRUCTURE_FILE=$(find "$(Build.SourcesDirectory)" -maxdepth 1 -name "$EXPECTED_FILENAME_PATTERN" -type f | head -1)

      if [ -z "$STRUCTURE_FILE" ]; then
        echo "❌ No FE structure file found matching '$EXPECTED_FILENAME_PATTERN'!"
        echo "Available .md files:"
        ls -la "$(Build.SourcesDirectory)"/*.md 2>/dev/null || echo "No .md files found."
        echo "Checking for any structure files:"
        ls -la "$(Build.SourcesDirectory)"/*structure*.md 2>/dev/null || echo "No structure files found."
        exit 1
      fi

      echo "✅ FE structure file found: $STRUCTURE_FILE"
      FILENAME=$(basename "$STRUCTURE_FILE")

      # Set pipeline variables for subsequent tasks
      echo "##vso[task.setvariable variable=StructureFileName]$FILENAME"
      echo "##vso[task.setvariable variable=StructureFilePath]$STRUCTURE_FILE"

      echo "--- FE Structure File Preview (first 20 lines) ---"
      head -20 "$STRUCTURE_FILE"
      echo "--- End Preview ---"
    displayName: 'Verify FE Structure File and Set Variables'