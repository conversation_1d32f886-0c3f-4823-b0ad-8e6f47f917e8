# KWh Usage Dashboard

A comprehensive electricity usage tracking and prediction dashboard built with React, TypeScript, and Shadcn/ui charts.

## Features

### 📊 Interactive Charts
- **Line Chart**: Shows usage trends over time
- **Bar Chart**: Displays daily usage as bars
- **Area Chart**: Visualizes usage patterns with filled areas
- **Real-time Predictions**: 7-day forecast with confidence levels

### 📈 Advanced Analytics
- **Usage Statistics**: Total usage, daily averages, and trend analysis
- **Pattern Recognition**: Identifies weekly patterns and seasonal trends
- **Anomaly Detection**: Highlights unusual usage days
- **AI Insights**: Provides actionable recommendations

### 🛠️ Data Management
- **Manual Entry**: Add individual data points with date picker
- **CSV Import/Export**: Bulk data operations
- **Real-time Updates**: Charts update immediately when data changes
- **Data Validation**: Ensures data integrity

## Getting Started

### 1. Access the Dashboard
Navigate to: `http://localhost:4200/kwh-dashboard`

No login required - this is a public demo dashboard.

### 2. View Sample Data
The dashboard comes pre-loaded with sample KWh usage data to demonstrate all features.

### 3. Manage Your Data
Click the **Settings** button (⚙️) to open the Data Manager where you can:
- Add new daily usage readings
- Import data from CSV files
- Export your data for backup
- Remove incorrect entries

## Data Format

### Manual Entry
- **Date**: Select any date using the date picker
- **KWh Usage**: Enter the daily electricity consumption (e.g., 15.2)

### CSV Import Format
```csv
Date,KWh,Day
2025-01-01,15.2,Mon
2025-01-02,14.8,Tue
2025-01-03,16.1,Wed
```

**Note**: The "Day" column is optional and will be auto-calculated if not provided.

## Understanding the Dashboard

### Statistics Cards
1. **Total Usage**: Sum of all recorded usage
2. **Daily Average**: Mean daily consumption
3. **Last Week Avg**: Recent 7-day average with trend indicator
4. **Predictions**: Number of forecasted days

### Chart Features
- **Blue Line/Bars**: Actual recorded usage
- **Orange Dashed Line**: Predicted future usage
- **Tooltips**: Hover over data points for detailed information
- **Responsive**: Automatically adjusts to screen size

### Insights Panel
The AI-powered insights analyze your usage patterns and provide:
- Trend analysis (increasing/decreasing/stable)
- Anomaly detection
- Day-of-week patterns
- Personalized recommendations

### Prediction Algorithm
The dashboard uses a sophisticated prediction model that combines:
- **Linear Regression**: Identifies overall trends
- **Moving Average**: Smooths out short-term fluctuations
- **Seasonal Patterns**: Accounts for day-of-week variations
- **Confidence Scoring**: Estimates prediction reliability

## Tips for Best Results

### Data Quality
- Enter data consistently (same time each day)
- Ensure accurate readings from your electricity meter
- Include at least 7-14 days of data for meaningful predictions

### Pattern Recognition
- The more data you provide, the better the predictions
- Weekly patterns become visible after 2-3 weeks of data
- Seasonal adjustments improve with longer data history

### Using Predictions
- Higher confidence scores (>80%) indicate more reliable predictions
- Use predictions for budgeting and usage planning
- Monitor actual vs predicted to validate accuracy

## Technical Details

### Built With
- **React 19** with TypeScript
- **Shadcn/ui** components
- **Recharts** for data visualization
- **Tailwind CSS** for styling
- **Lucide React** for icons

### Prediction Methods
1. **Linear Regression**: Calculates trend slope and intercept
2. **Moving Average**: Uses 7-day rolling average
3. **Seasonal Adjustment**: Applies day-of-week multipliers
4. **Weighted Combination**: Blends methods for optimal accuracy

### Data Storage
- Currently uses local state (data resets on page refresh)
- Future versions will include persistent storage
- CSV export provides data backup capability

## Troubleshooting

### No Predictions Showing
- Ensure you have at least 3 data points
- Check that dates are in correct format (YYYY-MM-DD)
- Verify KWh values are positive numbers

### Charts Not Updating
- Refresh the page if charts appear frozen
- Check browser console for any JavaScript errors
- Ensure data is properly formatted

### CSV Import Issues
- Verify CSV has proper headers (Date, KWh)
- Check date format matches YYYY-MM-DD
- Ensure KWh values are numeric

## Future Enhancements

- [ ] Persistent data storage
- [ ] Multiple meter support
- [ ] Cost calculations
- [ ] Email/SMS alerts
- [ ] Mobile app version
- [ ] Advanced forecasting models
- [ ] Energy efficiency recommendations
- [ ] Integration with smart meters

## Support

For questions or issues with the KWh Dashboard, please check:
1. This README file
2. Browser developer console for errors
3. CSV format requirements
4. Data validation rules

---

**Note**: This dashboard is designed for demonstration purposes. For production use, consider implementing proper data persistence and user authentication.
