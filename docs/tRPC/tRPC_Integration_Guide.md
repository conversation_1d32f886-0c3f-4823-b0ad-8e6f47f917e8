# tRPC Integration Guide for Introspection.Finance.Client

## Overview

tRPC (TypeScript Remote Procedure Call) is a library that enables end-to-end typesafe APIs. This guide outlines how to integrate tRPC into the existing microservices architecture while maintaining compatibility with the current REST API structure.

## Why tRPC for This Project?

### Benefits
- **End-to-end Type Safety**: Shared types between client and server
- **Automatic API Documentation**: Self-documenting through TypeScript
- **Better Developer Experience**: IntelliSense and compile-time error checking
- **Reduced Boilerplate**: No need for manual API client code
- **Real-time Subscriptions**: Built-in WebSocket support for live data

### Current Architecture Compatibility
tRPC can be gradually introduced alongside existing REST APIs without breaking changes.

## Architecture Integration

### Current vs tRPC Architecture

```mermaid
graph TB
    subgraph "Current REST Architecture"
        ClientREST[React Client]
        AxiosREST[Axios Client]
        IDPREST[Identity Service REST]
        FinanceREST[Finance Service REST]
        CalcREST[Calc Service REST]
        
        ClientREST --> AxiosREST
        AxiosREST --> IDPREST
        AxiosREST --> FinanceREST
        AxiosREST --> CalcREST
    end
    
    subgraph "Proposed tRPC Architecture"
        ClientTRPC[React Client]
        TRPCClient[tRPC Client]
        TRPCRouter[tRPC Router]
        IDPtRPC[Identity Service tRPC]
        FinancetRPC[Finance Service tRPC]
        CalctRPC[Calc Service tRPC]
        
        ClientTRPC --> TRPCClient
        TRPCClient --> TRPCRouter
        TRPCRouter --> IDPtRPC
        TRPCRouter --> FinancetRPC
        TRPCRouter --> CalctRPC
    end
    
    style TRPCRouter fill:#e1f5fe
    style TRPCClient fill:#f3e5f5
```

## Implementation Strategy

### Phase 1: tRPC Gateway Setup
Create a tRPC gateway that proxies to existing microservices:

```typescript
// server/trpc/router.ts
import { initTRPC } from '@trpc/server';
import { z } from 'zod';

const t = initTRPC.create();

export const appRouter = t.router({
  // Identity Service Procedures
  auth: t.router({
    login: t.procedure
      .input(z.object({
        email: z.string().email(),
        password: z.string().min(1),
      }))
      .mutation(async ({ input }) => {
        // Proxy to existing Identity service
        const response = await fetch(`${IDP_SERVICE_URL}/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input),
        });
        return response.json();
      }),
      
    register: t.procedure
      .input(z.object({
        email: z.string().email(),
        password: z.string().min(8),
        clientId: z.string(),
        clientOrigin: z.enum(['Excelytics']),
      }))
      .mutation(async ({ input }) => {
        // Proxy to existing Identity service
        const response = await fetch(`${IDP_SERVICE_URL}/auth/register`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(input),
        });
        return response.json();
      }),
  }),
  
  // Finance Service Procedures
  finance: t.router({
    getAnalytics: t.procedure
      .query(async () => {
        // Proxy to Finance service
        const response = await fetch(`${FINANCE_SERVICE_URL}/analytics`);
        return response.json();
      }),
      
    getReports: t.procedure
      .input(z.object({
        dateRange: z.object({
          start: z.date(),
          end: z.date(),
        }).optional(),
      }))
      .query(async ({ input }) => {
        const params = new URLSearchParams();
        if (input?.dateRange) {
          params.set('start', input.dateRange.start.toISOString());
          params.set('end', input.dateRange.end.toISOString());
        }
        
        const response = await fetch(`${FINANCE_SERVICE_URL}/reports?${params}`);
        return response.json();
      }),
  }),
  
  // Calc Service Procedures
  calc: t.router({
    uploadExcel: t.procedure
      .input(z.object({
        file: z.instanceof(File),
      }))
      .mutation(async ({ input }) => {
        const formData = new FormData();
        formData.append('file', input.file);
        
        const response = await fetch(`${CALC_SERVICE_URL}/excel/upload`, {
          method: 'POST',
          body: formData,
        });
        return response.json();
      }),
      
    getProcessingStatus: t.procedure
      .input(z.object({
        fileId: z.string(),
      }))
      .query(async ({ input }) => {
        const response = await fetch(`${CALC_SERVICE_URL}/status/${input.fileId}`);
        return response.json();
      }),
      
    // Real-time processing updates
    subscribeToProcessing: t.procedure
      .input(z.object({
        fileId: z.string(),
      }))
      .subscription(async function* ({ input }) {
        // WebSocket connection to Calc service
        const ws = new WebSocket(`${CALC_WS_URL}/processing/${input.fileId}`);
        
        yield* (async function* () {
          for await (const message of ws) {
            yield JSON.parse(message.data);
          }
        })();
      }),
  }),
});

export type AppRouter = typeof appRouter;
```

### Phase 2: Client Integration
Set up tRPC client in the React application:

```typescript
// client/src/lib/trpc.ts
import { createTRPCReact } from '@trpc/react-query';
import type { AppRouter } from '../../../server/trpc/router';

export const trpc = createTRPCReact<AppRouter>();
```

```typescript
// client/src/lib/trpcClient.ts
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import type { AppRouter } from '../../../server/trpc/router';

export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: '/api/trpc',
      headers() {
        const token = localStorage.getItem('authToken');
        return token ? { authorization: `Bearer ${token}` } : {};
      },
    }),
  ],
});
```

### Phase 3: React Integration
Wrap the app with tRPC providers:

```typescript
// client/src/main.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { trpc, trpcClient } from '@/lib/trpc';

const queryClient = new QueryClient();

root.render(
  <React.StrictMode>
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <Router>
            <App />
          </Router>
        </AuthProvider>
      </QueryClientProvider>
    </trpc.Provider>
  </React.StrictMode>
);
```

## Usage Examples

### Authentication with tRPC
```typescript
// client/src/hooks/useTRPCAuth.ts
import { trpc } from '@/lib/trpc';

export function useTRPCAuth() {
  const loginMutation = trpc.auth.login.useMutation({
    onSuccess: (data) => {
      localStorage.setItem('authToken', data.token);
      // Handle successful login
    },
    onError: (error) => {
      console.error('Login failed:', error.message);
    },
  });
  
  const registerMutation = trpc.auth.register.useMutation({
    onSuccess: (data) => {
      localStorage.setItem('authToken', data.token);
      // Handle successful registration
    },
  });
  
  return {
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    isLoggingIn: loginMutation.isLoading,
    isRegistering: registerMutation.isLoading,
  };
}
```

### Data Fetching with tRPC
```typescript
// client/src/components/AnalyticsDashboard.tsx
import { trpc } from '@/lib/trpc';

export function AnalyticsDashboard() {
  const { data: analytics, isLoading, error } = trpc.finance.getAnalytics.useQuery();
  
  const { data: reports } = trpc.finance.getReports.useQuery({
    dateRange: {
      start: new Date('2024-01-01'),
      end: new Date(),
    },
  });
  
  if (isLoading) return <div>Loading analytics...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h2>Analytics Dashboard</h2>
      {analytics && <AnalyticsChart data={analytics} />}
      {reports && <ReportsTable data={reports} />}
    </div>
  );
}
```

### File Upload with Progress
```typescript
// client/src/components/FileUploadTRPC.tsx
import { trpc } from '@/lib/trpc';

export function FileUploadTRPC() {
  const [file, setFile] = useState<File | null>(null);
  
  const uploadMutation = trpc.calc.uploadExcel.useMutation({
    onSuccess: (data) => {
      console.log('Upload successful:', data);
      // Start polling for processing status
    },
  });
  
  const handleUpload = () => {
    if (file) {
      uploadMutation.mutate({ file });
    }
  };
  
  return (
    <div>
      <input
        type="file"
        accept=".xlsx,.xls"
        onChange={(e) => setFile(e.target.files?.[0] || null)}
      />
      <button
        onClick={handleUpload}
        disabled={!file || uploadMutation.isLoading}
      >
        {uploadMutation.isLoading ? 'Uploading...' : 'Upload'}
      </button>
    </div>
  );
}
```

### Real-time Subscriptions
```typescript
// client/src/components/ProcessingStatus.tsx
import { trpc } from '@/lib/trpc';

export function ProcessingStatus({ fileId }: { fileId: string }) {
  const [status, setStatus] = useState<string>('pending');
  
  trpc.calc.subscribeToProcessing.useSubscription(
    { fileId },
    {
      onData: (data) => {
        setStatus(data.status);
        if (data.progress) {
          setProgress(data.progress);
        }
      },
      onError: (error) => {
        console.error('Subscription error:', error);
      },
    }
  );
  
  return (
    <div>
      <p>Processing Status: {status}</p>
      {progress && <ProgressBar value={progress} />}
    </div>
  );
}
```

## tRPC Data Flow Diagrams

### Authentication Flow with tRPC

```mermaid
sequenceDiagram
    participant C as Client
    participant TR as tRPC Router
    participant IDP as Identity Service
    
    C->>TR: trpc.auth.login.mutate(credentials)
    TR->>IDP: POST /auth/login
    IDP->>TR: { token, user }
    TR->>C: Typed response
    C->>C: Store token & update state
    
    Note over C,IDP: Type safety maintained end-to-end
```

### File Processing Flow with tRPC

```mermaid
sequenceDiagram
    participant C as Client
    participant TR as tRPC Router
    participant Calc as Calc Service
    participant WS as WebSocket
    
    C->>TR: trpc.calc.uploadExcel.mutate({ file })
    TR->>Calc: POST /excel/upload (FormData)
    Calc->>TR: { fileId, status: 'processing' }
    TR->>C: Typed response with fileId
    
    C->>TR: trpc.calc.subscribeToProcessing({ fileId })
    TR->>WS: Connect to processing updates
    
    loop Processing Updates
        WS->>TR: { status, progress }
        TR->>C: Real-time typed updates
    end
    
    WS->>TR: { status: 'completed', data }
    TR->>C: Final result with processed data
```

### Data Fetching with Caching

```mermaid
graph TB
    subgraph "Client Layer"
        Component[React Component]
        TRPCHook[tRPC Hook]
        ReactQuery[React Query Cache]
    end
    
    subgraph "Network Layer"
        TRPCClient[tRPC Client]
        HTTPBatch[HTTP Batch Link]
    end
    
    subgraph "Server Layer"
        TRPCRouter[tRPC Router]
        Microservice[Backend Service]
    end
    
    Component --> TRPCHook
    TRPCHook --> ReactQuery
    ReactQuery --> TRPCClient
    TRPCClient --> HTTPBatch
    HTTPBatch --> TRPCRouter
    TRPCRouter --> Microservice
    
    ReactQuery -.->|Cache Hit| TRPCHook
    
    style ReactQuery fill:#e8f5e8
    style TRPCRouter fill:#e1f5fe
```

## Installation & Setup

### Dependencies
```bash
# Install tRPC packages
bun add @trpc/server @trpc/client @trpc/react-query @trpc/next

# Install additional dependencies
bun add @tanstack/react-query zod

# Install dev dependencies
bun add -D @types/ws ws
```

### Project Structure Changes
```
server/
├── trpc/
│   ├── router.ts          # Main tRPC router
│   ├── context.ts         # Request context
│   ├── middleware.ts      # Authentication middleware
│   └── routers/
│       ├── auth.ts        # Authentication procedures
│       ├── finance.ts     # Finance procedures
│       └── calc.ts        # Calculation procedures
client/src/
├── lib/
│   ├── trpc.ts           # tRPC React hooks
│   └── trpcClient.ts     # tRPC client configuration
└── hooks/
    ├── useTRPCAuth.ts    # Authentication hooks
    └── useTRPCData.ts    # Data fetching hooks
```

### Server Configuration
```typescript
// server/trpc/context.ts
import type { CreateNextContextOptions } from '@trpc/server/adapters/next';

export async function createContext({ req, res }: CreateNextContextOptions) {
  const token = req.headers.authorization?.replace('Bearer ', '');

  return {
    req,
    res,
    token,
    user: token ? await validateToken(token) : null,
  };
}

export type Context = Awaited<ReturnType<typeof createContext>>;
```

```typescript
// server/trpc/middleware.ts
import { TRPCError } from '@trpc/server';
import { t } from './router';

export const isAuthenticated = t.middleware(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }

  return next({
    ctx: {
      ...ctx,
      user: ctx.user,
    },
  });
});

export const protectedProcedure = t.procedure.use(isAuthenticated);
```

## Migration Strategy

### Gradual Migration Approach
1. **Phase 1**: Set up tRPC alongside existing REST APIs
2. **Phase 2**: Migrate authentication endpoints
3. **Phase 3**: Migrate data fetching endpoints
4. **Phase 4**: Add real-time features with subscriptions
5. **Phase 5**: Deprecate REST endpoints

### Compatibility Layer
```typescript
// Maintain backward compatibility
export const legacyApiClient = {
  // Keep existing Axios client for gradual migration
  auth: AuthMethods,
  calc: { uploadExcelFile },
  finance: {}, // To be migrated
};

export const modernApiClient = {
  // New tRPC client
  trpc: trpcClient,
};
```

## Performance Benefits

### Request Batching
```typescript
// Multiple tRPC calls are automatically batched
const Component = () => {
  const analytics = trpc.finance.getAnalytics.useQuery();
  const reports = trpc.finance.getReports.useQuery();
  const status = trpc.calc.getProcessingStatus.useQuery({ fileId: '123' });

  // All three requests are batched into a single HTTP request
};
```

### Intelligent Caching
```typescript
// React Query integration provides intelligent caching
const { data, isLoading, error } = trpc.finance.getAnalytics.useQuery(
  undefined, // input
  {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  }
);
```

## Error Handling

### Type-safe Error Handling
```typescript
// Errors are typed and provide detailed information
const mutation = trpc.auth.login.useMutation({
  onError: (error) => {
    if (error.data?.code === 'UNAUTHORIZED') {
      setError('Invalid credentials');
    } else if (error.data?.code === 'TOO_MANY_REQUESTS') {
      setError('Too many login attempts. Please try again later.');
    } else {
      setError('An unexpected error occurred');
    }
  },
});
```

## Testing with tRPC

### Unit Testing Procedures
```typescript
// server/trpc/__tests__/auth.test.ts
import { createContext } from '../context';
import { appRouter } from '../router';

describe('Auth procedures', () => {
  test('login with valid credentials', async () => {
    const ctx = await createContext({ req: mockReq, res: mockRes });
    const caller = appRouter.createCaller(ctx);

    const result = await caller.auth.login({
      email: '<EMAIL>',
      password: 'password123',
    });

    expect(result.token).toBeDefined();
    expect(result.user.email).toBe('<EMAIL>');
  });
});
```

### Integration Testing
```typescript
// client/src/__tests__/trpc.test.tsx
import { renderHook } from '@testing-library/react';
import { trpc } from '@/lib/trpc';

test('analytics query returns data', async () => {
  const { result, waitFor } = renderHook(
    () => trpc.finance.getAnalytics.useQuery(),
    { wrapper: TRPCTestWrapper }
  );

  await waitFor(() => expect(result.current.isSuccess).toBe(true));
  expect(result.current.data).toBeDefined();
});
```

## Monitoring & Debugging

### Development Tools
```typescript
// Enable tRPC dev tools in development
const trpcClient = trpc.createClient({
  links: [
    loggerLink({
      enabled: (opts) =>
        process.env.NODE_ENV === 'development' ||
        (opts.direction === 'down' && opts.result instanceof Error),
    }),
    httpBatchLink({ url: '/api/trpc' }),
  ],
});
```

### Production Monitoring
```typescript
// Add telemetry and monitoring
const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: '/api/trpc',
      headers() {
        return {
          'x-request-id': generateRequestId(),
          'x-client-version': process.env.REACT_APP_VERSION,
        };
      },
    }),
  ],
});
```

## Conclusion

tRPC provides significant benefits for the Introspection.Finance.Client project:

1. **Type Safety**: End-to-end TypeScript safety reduces runtime errors
2. **Developer Experience**: Better IntelliSense and compile-time checking
3. **Performance**: Automatic request batching and intelligent caching
4. **Real-time Features**: Built-in subscription support for live updates
5. **Maintainability**: Self-documenting APIs through TypeScript

The gradual migration approach allows for introducing tRPC without disrupting existing functionality, making it a low-risk, high-reward enhancement to the current architecture.
