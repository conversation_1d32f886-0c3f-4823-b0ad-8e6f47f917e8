# tRPC Authentication Integration Guide

## Overview

This guide explains how authentication works in our tRPC-based microservices architecture, focusing on the integration between the Finance Client and Identity Provider (IdP) service.

## Authentication Flow

### 1. User Registration
```typescript
// Client-side registration
const registrationResult = await trpc.identity.register.mutate({
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  firstName: 'John',
  lastName: 'Doe',
  company: 'Acme Corp'
});

// Expected response structure
interface RegistrationResponse {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company?: string;
  clientId: string;
  clientOrigin: number;
  roles: string[];
  isActive: boolean;
  createdAt: string;
  emailVerified: boolean;
}
```

### 2. User Login
```typescript
// Client-side login
const loginResult = await trpc.identity.login.mutate({
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  rememberMe: false
});

// Expected response structure
interface LoginResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    company?: string;
    role: string;
    emailVerified: boolean;
    clientId: string;
    clientOrigin: number;
    roles: string[];
    isActive: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}
```

## AccessTokenPayload Structure

The `AccessTokenPayload` from `excelytics.shared-dtos` defines the structure of decoded JWT tokens:

```typescript
interface AccessTokenPayload {
  userId: string;
  email: string;
  clientId: string;
  clientOrigin: number;
  roles: string[];
  tokenType: 'ACCESS';
  iat: number; // Issued at
  exp: number; // Expires at
}
```

### Express Request Integration

The Express Request object is extended to include the decoded token payload:

```typescript
// client/types/express.d.ts
declare global {
  namespace Express {
    interface Request {
      user?: AccessTokenPayload;
    }
  }
}
```

### Client-side User Type

The client-side User type omits the `tokenType` field:

```typescript
// client/types/auth.types.ts
export type User = Omit<AccessTokenPayload, 'tokenType'>;
```

## Authentication Context

### AuthContext Structure
```typescript
interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  user: User | null;
  logout: () => void;
  isLoading: boolean;
  login: (credentials: LoginDTO) => Promise<void>;
  register: (details: RegisterDTO) => Promise<void>;
}
```

### Token Handling
```typescript
// Token storage and validation
const handleAuthSuccess = (newToken: string) => {
  try {
    const decoded = jwtDecode<AccessTokenPayload>(newToken);
    localStorage.setItem('authToken', newToken);
    setToken(newToken);
    setUser(decoded); // User type (without tokenType)
  } catch (error) {
    console.error('Failed to decode token:', error);
    logout();
  }
};
```

## tRPC Context Integration

### Server-side Context Creation
```typescript
// server/trpc/context.ts
export async function createContext({ req }: CreateContextOptions) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  let userId: string | undefined;
  let user: AccessTokenPayload | undefined;
  
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as AccessTokenPayload;
      userId = decoded.userId;
      user = decoded;
    } catch (error) {
      console.warn('Invalid token:', error);
    }
  }
  
  return {
    req,
    userId,
    user, // Full AccessTokenPayload
  };
}
```

### Protected Procedures
```typescript
// Using authentication in tRPC procedures
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.userId || !ctx.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in',
    });
  }
  
  return next({
    ctx: {
      ...ctx,
      userId: ctx.userId, // Guaranteed to exist
      user: ctx.user,     // Full AccessTokenPayload
    },
  });
});
```

## Identity Service Integration

### Direct API Calls
The tRPC helper includes methods for direct Identity service communication:

```typescript
// Create test user
const userResult = await TRPCTestHelper.createTestUserInIdentityService('test');

// Login user
const loginResult = await TRPCTestHelper.loginUserInIdentityService(
  '<EMAIL>',
  'password'
);

// Expected response structure
interface AuthResponse {
  success: boolean;
  user?: AuthUser;
  tokens?: AuthTokens;
  response?: any;
  status: number;
  error?: string;
}
```

### URI Constants
All Identity service endpoints are centralized in URI constants:

```typescript
// tests/config/idp.uri.constants.ts
export const IDP_URI_CONSTANTS = {
  AUTH: {
    REGISTER: 'api/v1/auth/register',
    LOGIN: 'api/v1/auth/login',
    REFRESH_TOKEN: 'api/v1/auth/refresh-token',
    LOGOUT: 'api/v1/auth/logout'
  },
  USER: {
    GET: 'api/v1/identity/me',
    UPDATE: 'api/v1/identity/me'
  }
};
```

## Testing Authentication

### Type Validation Tests
```typescript
// Verify response structure matches AccessTokenPayload
it('should return tokens compatible with AccessTokenPayload', async () => {
  const result = await trpcClient.identity.login.mutate({
    email: testUser.email,
    password: testUser.password,
    rememberMe: false
  });
  
  // Verify token structure
  expect(result.tokens.accessToken).toBeDefined();
  expect(result.tokens.refreshToken).toBeDefined();
  expect(result.tokens.expiresIn).toBeGreaterThan(0);
  
  // In real implementation, decode and validate JWT
  const mockPayload: AccessTokenPayload = {
    userId: result.user.id,
    email: result.user.email,
    clientId: result.user.clientId,
    clientOrigin: result.user.clientOrigin,
    roles: result.user.roles,
    tokenType: 'ACCESS',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600
  };
  
  expect(mockPayload).toMatchObject({
    userId: expect.any(String),
    email: expect.any(String),
    clientId: expect.any(String),
    clientOrigin: expect.any(Number),
    roles: expect.any(Array),
    tokenType: 'ACCESS'
  });
});
```

### Integration Tests
```typescript
// Test tRPC and direct Identity service compatibility
it('should match response formats between tRPC and Identity service', async () => {
  const directResult = await TRPCTestHelper.loginUserInIdentityService(
    testUser.email,
    testUser.password
  );
  
  const trpcResult = await trpcClient.identity.login.mutate({
    email: testUser.email,
    password: testUser.password,
    rememberMe: false
  });
  
  // Both should have compatible structures
  expect(directResult.success).toBe(true);
  expect(directResult.user?.email).toBe(trpcResult.user.email);
  expect(directResult.tokens).toBeDefined();
  expect(trpcResult.tokens).toBeDefined();
});
```

## Best Practices

### 1. Token Management
- Store tokens securely in localStorage
- Validate token expiration before use
- Implement automatic token refresh
- Clear tokens on logout or error

### 2. Type Safety
- Use AccessTokenPayload for server-side token validation
- Use User type (without tokenType) for client-side state
- Validate response structures in tests
- Maintain consistency between tRPC and direct API calls

### 3. Error Handling
- Handle authentication errors gracefully
- Provide clear error messages to users
- Log authentication failures for monitoring
- Implement proper fallback mechanisms

### 4. Security
- Validate JWT signatures on the server
- Use HTTPS for all authentication requests
- Implement proper CORS policies
- Sanitize user input in authentication flows

## Migration Notes

When migrating from direct API calls to tRPC:

1. **Response Structure**: Ensure tRPC responses match existing API response formats
2. **Type Definitions**: Update type imports to use shared DTOs
3. **Error Handling**: Adapt error handling to tRPC error format
4. **Testing**: Update tests to validate both tRPC and direct API compatibility

This ensures seamless integration between the Finance Client and Identity Provider while maintaining type safety and consistency across the microservices architecture.
