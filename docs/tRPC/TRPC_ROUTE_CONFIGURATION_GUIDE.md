# tRPC Route Configuration Guide

## Table of Contents
1. [Overview](#overview)
2. [Core Concepts](#core-concepts)
3. [Router Structure](#router-structure)
4. [Procedure Types](#procedure-types)
5. [Handlers](#handlers)
6. [Middleware](#middleware)
7. [Context](#context)
8. [Input Validation](#input-validation)
9. [Error Handling](#error-handling)
10. [Best Practices](#best-practices)

## Overview

tRPC (TypeScript Remote Procedure Call) is a library that allows you to build end-to-end typesafe APIs without code generation. It provides a way to define API routes (procedures) that are automatically typed on both the client and server sides.

## Core Concepts

### What is tRPC?
- **Type-safe**: Full TypeScript support with automatic type inference
- **No code generation**: Types are inferred directly from your API definition
- **Framework agnostic**: Works with Express, Next.js, Fastify, etc.
- **Real-time support**: Built-in support for subscriptions via WebSockets

### Key Components
1. **Router**: Container for organizing related procedures
2. **Procedure**: Individual API endpoints (queries, mutations, subscriptions)
3. **Handler**: The actual function that processes the request
4. **Context**: Shared data available to all procedures
5. **Middleware**: Functions that run before/after procedures

## Router Structure

### Basic Router Setup
```typescript
import { createTRPCRouter } from './trpc';
import { userRouter } from './routers/user';
import { authRouter } from './routers/auth';

export const appRouter = createTRPCRouter({
  // Health check procedure
  healthCheck: publicProcedure.query(() => {
    return { status: 'healthy', timestamp: new Date().toISOString() };
  }),
  
  // Nested routers
  user: userRouter,
  auth: authRouter,
});

export type AppRouter = typeof appRouter;
```

### Nested Routers
Routers can be nested to organize related functionality:

```typescript
// routers/user.ts
export const userRouter = createTRPCRouter({
  getProfile: protectedProcedure.query(({ ctx }) => {
    return getUserProfile(ctx.userId);
  }),
  
  updateProfile: protectedProcedure
    .input(updateProfileSchema)
    .mutation(({ input, ctx }) => {
      return updateUserProfile(ctx.userId, input);
    }),
});

// routers/auth.ts
export const authRouter = createTRPCRouter({
  login: publicProcedure
    .input(loginSchema)
    .mutation(({ input }) => {
      return authenticateUser(input);
    }),
    
  logout: protectedProcedure.mutation(({ ctx }) => {
    return logoutUser(ctx.userId);
  }),
});
```

## Procedure Types

### 1. Query
Used for **reading** data (GET requests in REST terms):

```typescript
// Simple query
getUser: publicProcedure.query(() => {
  return { id: 1, name: 'John Doe' };
});

// Query with input
getUserById: publicProcedure
  .input(z.object({ id: z.string() }))
  .query(({ input }) => {
    return getUserById(input.id);
  });
```

### 2. Mutation
Used for **modifying** data (POST/PUT/DELETE in REST terms):

```typescript
// Create user
createUser: publicProcedure
  .input(createUserSchema)
  .mutation(({ input }) => {
    return createUser(input);
  });

// Update user
updateUser: protectedProcedure
  .input(updateUserSchema)
  .mutation(({ input, ctx }) => {
    return updateUser(ctx.userId, input);
  });

// Delete user
deleteUser: protectedProcedure
  .input(z.object({ id: z.string() }))
  .mutation(({ input, ctx }) => {
    return deleteUser(input.id, ctx.userId);
  });
```

### 3. Subscription
Used for **real-time** data streaming:

```typescript
// Simple subscription
onUserUpdate: publicProcedure.subscription(() => {
  return observable<User>((emit) => {
    const unsubscribe = userUpdateEmitter.on('update', emit.next);
    return unsubscribe;
  });
});

// Subscription with input
onChatMessage: protectedProcedure
  .input(z.object({ roomId: z.string() }))
  .subscription(({ input, ctx }) => {
    return observable<ChatMessage>((emit) => {
      const unsubscribe = chatEmitter.on(`room:${input.roomId}`, emit.next);
      return unsubscribe;
    });
  });
```

## Handlers

### What is a Handler?
A handler is the actual function that processes the request and returns the response. It's the business logic of your API endpoint.

### Handler Structure
```typescript
const handler = ({ input, ctx }) => {
  // Business logic here
  return result;
};
```

### Handler Parameters
- **input**: The validated input data (if `.input()` was used)
- **ctx**: The context object containing shared data
- **meta**: Metadata about the procedure (rarely used)

### Handler Examples
```typescript
// Simple handler
getUsers: publicProcedure.query(() => {
  return database.users.findMany();
});

// Handler with input validation
createUser: publicProcedure
  .input(z.object({
    name: z.string().min(1),
    email: z.string().email(),
  }))
  .mutation(({ input }) => {
    return database.users.create({
      data: input,
    });
  });

// Handler with context
getMyProfile: protectedProcedure.query(({ ctx }) => {
  return database.users.findUnique({
    where: { id: ctx.userId },
  });
});

// Complex handler with error handling
updateUserProfile: protectedProcedure
  .input(updateProfileSchema)
  .mutation(async ({ input, ctx }) => {
    try {
      const user = await database.users.findUnique({
        where: { id: ctx.userId },
      });
      
      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }
      
      return await database.users.update({
        where: { id: ctx.userId },
        data: input,
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update profile',
        cause: error,
      });
    }
  });
```

### Handler Return Types
Handlers can return:
- **Synchronous data**: Direct values
- **Promises**: For async operations
- **Observables**: For subscriptions
- **Streams**: For file uploads/downloads

```typescript
// Synchronous
getConstant: publicProcedure.query(() => {
  return { value: 42 };
});

// Asynchronous
getUser: publicProcedure.query(async () => {
  return await database.users.findFirst();
});

// Observable (for subscriptions)
onUpdate: publicProcedure.subscription(() => {
  return observable<string>((emit) => {
    const interval = setInterval(() => {
      emit.next(`Update at ${new Date().toISOString()}`);
    }, 1000);
    
    return () => clearInterval(interval);
  });
});
```

## Middleware

### What is Middleware?
Middleware functions run before or after procedures, allowing you to:
- Authenticate users
- Log requests
- Transform input/output
- Add timing information
- Handle errors

### Creating Middleware
```typescript
// Authentication middleware
const authMiddleware = t.middleware(({ ctx, next }) => {
  if (!ctx.userId) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in',
    });
  }

  return next({
    ctx: {
      ...ctx,
      userId: ctx.userId, // Now guaranteed to exist
    },
  });
});

// Logging middleware
const loggerMiddleware = t.middleware(async ({ path, type, next }) => {
  const start = Date.now();
  console.log(`📞 ${type} ${path} - Started`);

  const result = await next();

  const duration = Date.now() - start;
  console.log(`✅ ${type} ${path} - Completed in ${duration}ms`);

  return result;
});

// Admin role middleware
const adminMiddleware = t.middleware(({ ctx, next }) => {
  if (!ctx.user?.roles?.includes('admin')) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'Admin access required',
    });
  }

  return next();
});
```

### Using Middleware
```typescript
// Create procedures with middleware
export const publicProcedure = t.procedure.use(loggerMiddleware);
export const protectedProcedure = t.procedure
  .use(loggerMiddleware)
  .use(authMiddleware);
export const adminProcedure = t.procedure
  .use(loggerMiddleware)
  .use(authMiddleware)
  .use(adminMiddleware);

// Use in routes
const userRouter = createTRPCRouter({
  // Public route
  getPublicInfo: publicProcedure.query(() => {
    return { message: 'This is public' };
  }),

  // Protected route
  getProfile: protectedProcedure.query(({ ctx }) => {
    return getUserProfile(ctx.userId); // userId is guaranteed to exist
  }),

  // Admin only route
  getAllUsers: adminProcedure.query(() => {
    return getAllUsers();
  }),
});
```

## Context

### What is Context?
Context is an object that contains data shared across all procedures in a request. It's created once per request and passed to all procedures and middleware.

### Creating Context
```typescript
// server/trpc/context.ts
import { inferAsyncReturnType } from '@trpc/server';
import { CreateNextContextOptions } from '@trpc/server/adapters/next';
import jwt from 'jsonwebtoken';

export async function createContext({ req, res }: CreateNextContextOptions) {
  // Extract token from headers
  const token = req.headers.authorization?.replace('Bearer ', '');

  let userId: string | undefined;
  let user: User | undefined;

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
      userId = decoded.userId;
      user = await getUserById(userId);
    } catch (error) {
      // Invalid token, but don't throw error here
      console.warn('Invalid token:', error);
    }
  }

  return {
    req,
    res,
    userId,
    user,
    db: database, // Database connection
    redis: redisClient, // Cache connection
  };
}

export type Context = inferAsyncReturnType<typeof createContext>;
```

### Using Context in Procedures
```typescript
const userRouter = createTRPCRouter({
  // Access user info from context
  getMyProfile: protectedProcedure.query(({ ctx }) => {
    return ctx.db.users.findUnique({
      where: { id: ctx.userId },
    });
  }),

  // Use database from context
  createPost: protectedProcedure
    .input(createPostSchema)
    .mutation(({ input, ctx }) => {
      return ctx.db.posts.create({
        data: {
          ...input,
          authorId: ctx.userId,
        },
      });
    }),

  // Use Redis for caching
  getCachedData: publicProcedure.query(async ({ ctx }) => {
    const cached = await ctx.redis.get('cached-data');
    if (cached) {
      return JSON.parse(cached);
    }

    const data = await fetchExpensiveData();
    await ctx.redis.setex('cached-data', 300, JSON.stringify(data));
    return data;
  }),
});
```

## Input Validation

### Using Zod for Validation
tRPC integrates seamlessly with Zod for input validation:

```typescript
import { z } from 'zod';

// Define schemas
const createUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email format'),
  age: z.number().int().min(18, 'Must be at least 18').max(120),
  role: z.enum(['user', 'admin']).default('user'),
  preferences: z.object({
    newsletter: z.boolean().default(false),
    theme: z.enum(['light', 'dark']).default('light'),
  }).optional(),
});

const updateUserSchema = createUserSchema.partial();

// Use in procedures
const userRouter = createTRPCRouter({
  create: publicProcedure
    .input(createUserSchema)
    .mutation(({ input }) => {
      // input is fully typed and validated
      return createUser(input);
    }),

  update: protectedProcedure
    .input(z.object({
      id: z.string(),
      data: updateUserSchema,
    }))
    .mutation(({ input, ctx }) => {
      return updateUser(input.id, input.data, ctx.userId);
    }),
});
```

### Complex Validation Examples
```typescript
// File upload validation
const uploadSchema = z.object({
  file: z.object({
    name: z.string(),
    size: z.number().max(10 * 1024 * 1024, 'File too large'), // 10MB
    type: z.string().regex(/^image\/(jpeg|png|gif)$/, 'Invalid file type'),
  }),
  metadata: z.object({
    alt: z.string().optional(),
    caption: z.string().max(500).optional(),
  }).optional(),
});

// Pagination validation
const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  filters: z.object({
    status: z.enum(['active', 'inactive']).optional(),
    category: z.string().optional(),
    dateRange: z.object({
      start: z.date(),
      end: z.date(),
    }).optional(),
  }).optional(),
});

// Search validation
const searchSchema = z.object({
  query: z.string().min(1, 'Search query required').max(100),
  filters: z.object({
    category: z.array(z.string()).optional(),
    tags: z.array(z.string()).optional(),
    priceRange: z.object({
      min: z.number().min(0),
      max: z.number().min(0),
    }).refine(data => data.max >= data.min, {
      message: 'Max price must be greater than or equal to min price',
    }).optional(),
  }).optional(),
});
```

## Error Handling

### tRPC Error Codes
tRPC provides standard HTTP-like error codes:

```typescript
import { TRPCError } from '@trpc/server';

// Available error codes:
// - BAD_REQUEST (400)
// - UNAUTHORIZED (401)
// - FORBIDDEN (403)
// - NOT_FOUND (404)
// - METHOD_NOT_SUPPORTED (405)
// - TIMEOUT (408)
// - CONFLICT (409)
// - PRECONDITION_FAILED (412)
// - PAYLOAD_TOO_LARGE (413)
// - UNPROCESSABLE_CONTENT (422)
// - TOO_MANY_REQUESTS (429)
// - CLIENT_CLOSED_REQUEST (499)
// - INTERNAL_SERVER_ERROR (500)

// Throwing errors
const userRouter = createTRPCRouter({
  getUser: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const user = await database.users.findUnique({
        where: { id: input.id },
      });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      return user;
    }),

  createUser: publicProcedure
    .input(createUserSchema)
    .mutation(async ({ input }) => {
      try {
        return await database.users.create({
          data: input,
        });
      } catch (error) {
        if (error.code === 'P2002') { // Prisma unique constraint error
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'User with this email already exists',
            cause: error,
          });
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create user',
          cause: error,
        });
      }
    }),
});
```

### Global Error Handling
You can also handle errors globally using middleware:

```typescript
const errorHandlingMiddleware = t.middleware(async ({ next }) => {
  try {
    return await next();
  } catch (error) {
    // Log error
    console.error('tRPC Error:', error);

    // Transform database errors
    if (error.code === 'P2002') {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'Resource already exists',
      });
    }

    // Re-throw tRPC errors as-is
    if (error instanceof TRPCError) {
      throw error;
    }

    // Transform unknown errors
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
      cause: error,
    });
  }
});

export const publicProcedure = t.procedure.use(errorHandlingMiddleware);
```

## Best Practices

### 1. Router Organization
```typescript
// ✅ Good: Organize by feature/domain
routers/
├── auth.ts          // Authentication related procedures
├── user.ts          // User management
├── post.ts          // Post operations
├── comment.ts       // Comment operations
└── admin.ts         // Admin-only procedures

// ❌ Bad: Organize by procedure type
routers/
├── queries.ts       // All queries mixed together
├── mutations.ts     // All mutations mixed together
└── subscriptions.ts // All subscriptions mixed together
```

### 2. Input Validation
```typescript
// ✅ Good: Comprehensive validation
const createPostSchema = z.object({
  title: z.string().min(1, 'Title required').max(200, 'Title too long'),
  content: z.string().min(10, 'Content too short').max(10000, 'Content too long'),
  tags: z.array(z.string()).max(10, 'Too many tags').optional(),
  publishedAt: z.date().optional(),
  categoryId: z.string().uuid('Invalid category ID'),
});

// ❌ Bad: Minimal validation
const createPostSchema = z.object({
  title: z.string(),
  content: z.string(),
  tags: z.array(z.string()).optional(),
});
```

### 3. Error Messages
```typescript
// ✅ Good: User-friendly error messages
throw new TRPCError({
  code: 'BAD_REQUEST',
  message: 'Please provide a valid email address',
});

// ❌ Bad: Technical error messages
throw new TRPCError({
  code: 'BAD_REQUEST',
  message: 'Validation failed on field email with validator isEmail',
});
```

### 4. Context Usage
```typescript
// ✅ Good: Lean context with only necessary data
export async function createContext({ req }: CreateContextOptions) {
  const userId = await getUserIdFromToken(req.headers.authorization);

  return {
    userId,
    db: database,
    redis: redisClient,
  };
}

// ❌ Bad: Heavy context with unnecessary data
export async function createContext({ req }: CreateContextOptions) {
  const userId = await getUserIdFromToken(req.headers.authorization);
  const user = userId ? await getFullUserProfile(userId) : null;
  const permissions = user ? await getUserPermissions(user.id) : [];
  const settings = await getGlobalSettings();

  return {
    userId,
    user,
    permissions,
    settings,
    db: database,
    redis: redisClient,
    // ... many more properties
  };
}
```

### 5. Procedure Naming
```typescript
// ✅ Good: Clear, descriptive names
const userRouter = createTRPCRouter({
  getProfile: protectedProcedure.query(/* ... */),
  updateProfile: protectedProcedure.mutation(/* ... */),
  deleteAccount: protectedProcedure.mutation(/* ... */),
  listPosts: protectedProcedure.query(/* ... */),
  createPost: protectedProcedure.mutation(/* ... */),
});

// ❌ Bad: Unclear or inconsistent names
const userRouter = createTRPCRouter({
  profile: protectedProcedure.query(/* ... */),
  update: protectedProcedure.mutation(/* ... */),
  remove: protectedProcedure.mutation(/* ... */),
  posts: protectedProcedure.query(/* ... */),
  newPost: protectedProcedure.mutation(/* ... */),
});
```

### 6. Type Safety
```typescript
// ✅ Good: Leverage TypeScript fully
interface CreateUserInput {
  name: string;
  email: string;
  role: 'user' | 'admin';
}

const createUserSchema: z.ZodType<CreateUserInput> = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  role: z.enum(['user', 'admin']),
});

// ❌ Bad: Loose typing
const createUserSchema = z.object({
  name: z.any(),
  email: z.any(),
  role: z.any(),
});
```

### 7. Performance Considerations
```typescript
// ✅ Good: Efficient database queries
const getUserPosts = protectedProcedure
  .input(z.object({
    userId: z.string(),
    limit: z.number().max(100).default(10),
    cursor: z.string().optional(),
  }))
  .query(async ({ input, ctx }) => {
    const posts = await ctx.db.posts.findMany({
      where: { authorId: input.userId },
      take: input.limit + 1,
      cursor: input.cursor ? { id: input.cursor } : undefined,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        title: true,
        createdAt: true,
        _count: { select: { comments: true } },
      },
    });

    const hasNextPage = posts.length > input.limit;
    const items = hasNextPage ? posts.slice(0, -1) : posts;

    return {
      items,
      nextCursor: hasNextPage ? items[items.length - 1].id : null,
    };
  });

// ❌ Bad: Inefficient queries
const getUserPosts = protectedProcedure
  .query(async ({ ctx }) => {
    // Fetches ALL posts for ALL users
    const allPosts = await ctx.db.posts.findMany({
      include: {
        author: true,
        comments: {
          include: {
            author: true,
          },
        },
        tags: true,
      },
    });

    return allPosts.filter(post => post.authorId === ctx.userId);
  });
```

## Summary

tRPC provides a powerful, type-safe way to build APIs with:

- **Routers** that organize related procedures
- **Procedures** (queries, mutations, subscriptions) that define API endpoints
- **Handlers** that contain the business logic
- **Middleware** for cross-cutting concerns
- **Context** for shared request data
- **Input validation** with Zod schemas
- **Error handling** with standardized error codes

By following these patterns and best practices, you can build maintainable, type-safe APIs that provide excellent developer experience on both the server and client sides.
