# Microservices Registration Architecture Analysis & Proposals

## Current Architecture Overview

The Excelytics microservices architecture consists of 4 main services:

1. **Finance Client (FE)** - React frontend with tRPC client
2. **Finance Service (BE)** - Express backend with business logic
3. **Identity Provider (IdP)** - Authentication and user management
4. **Calculation Engine** - Excel processing and calculations

## Current Registration Flow Issues

### The Problem
The current registration process has a complex, multi-step flow that creates dependencies and potential inconsistencies:

1. **Organization Creation** - Must be created first in Finance service
2. **User Creation** - Business data stored in Finance service Users table
3. **Identity Registration** - Sensitive auth data stored in IdP Identity table

### Current Data Models

#### Finance Service Models

**Organization Model** (`Finance/src/models/internal/organization.model.ts`):
```typescript
class Organization {
  editionCode: EnumEditions;           // Subscription level
  organizationName: string;            // Company name
  organizationAccess?: OrganizationAccess[]; // Client access permissions
}
```

**User Model** (`Finance/src/models/internal/user.model.ts`):
```typescript
class User {
  fkOrganizationId: Ref<Organization>; // Link to organization
  userTypeCode: EnumUserTypes;         // User role/type
  editionCode: EnumEditions;           // Subscription level
  firstName: string;
  lastName: string;
  preferredName: string;
  email: string;                       // Business identifier
  cellNumber: string;
  isActive: boolean;
}
```

**Key Insight from User Model Comments**:
> "Its own _id. This is the primary key that the Identity model's clientId field refers to."

This means the Finance User._id becomes the IdP Identity.clientId, creating a tight coupling.

#### Identity Provider Models

**Identity Model** (from IdP service):
```typescript
class Identity {
  clientId: string;        // References Finance User._id
  clientOrigin: number;    // EnumClientOrigin
  clientPath: string;      // Service identifier
  email: string;           // Auth identifier
  password: string;        // Hashed password
  isActive: boolean;
  roles: string[];         // Permissions
}
```

## Current Flow Analysis

### Current Registration Flow (Problematic)

```mermaid
sequenceDiagram
    participant Client as Finance Client
    participant Finance as Finance Service
    participant IdP as Identity Provider
    
    Client->>Finance: 1. Create Organization
    Finance->>Finance: Store in Organization table
    Finance-->>Client: Return organizationId
    
    Client->>Finance: 2. Create User
    Finance->>Finance: Store in User table with fkOrganizationId
    Finance-->>Client: Return userId
    
    Client->>IdP: 3. Register Identity
    Note over Client,IdP: clientId = Finance User._id
    IdP->>IdP: Store in Identity table
    IdP-->>Client: Return tokens
    
    Note over Client,IdP: ❌ Issues:
    Note over Client,IdP: - Multiple API calls
    Note over Client,IdP: - Partial failure scenarios
    Note over Client,IdP: - Tight coupling
    Note over Client,IdP: - Complex error handling
```

### Current Authentication Flow

```mermaid
sequenceDiagram
    participant Client as Finance Client
    participant Finance as Finance Service
    participant IdP as Identity Provider
    
    Client->>IdP: 1. Login Request
    IdP->>IdP: Validate credentials
    IdP-->>Client: Return JWT tokens
    
    Client->>Finance: 2. API Request with JWT
    Finance->>IdP: 3. Token Introspection
    IdP-->>Finance: Token validation response
    Finance->>Finance: 4. Map to AccessTokenPayload
    Finance->>Finance: 5. Fetch user business data
    Finance-->>Client: Return business data
```

## Proposed Better Architectures

### Option 1: IdP-Orchestrated Registration (Recommended)

This approach makes the IdP the orchestrator of the registration process, handling the complexity internally.

```mermaid
sequenceDiagram
    participant Client as Finance Client
    participant IdP as Identity Provider
    participant Finance as Finance Service
    
    Client->>IdP: 1. Complete Registration Request
    Note over Client,IdP: {email, password, firstName, lastName, company, editionCode}
    
    IdP->>Finance: 2. Create Organization
    Finance->>Finance: Store Organization
    Finance-->>IdP: Return organizationId
    
    IdP->>Finance: 3. Create User
    Note over IdP,Finance: Include organizationId
    Finance->>Finance: Store User with fkOrganizationId
    Finance-->>IdP: Return userId
    
    IdP->>IdP: 4. Create Identity
    Note over IdP: clientId = Finance userId
    IdP->>IdP: Store Identity with clientId
    
    IdP-->>Client: 5. Return complete auth response
    Note over IdP,Client: {user, tokens, organization}
    
    Note over Client,Finance: ✅ Benefits:
    Note over Client,Finance: - Single API call
    Note over Client,Finance: - Atomic transaction
    Note over Client,Finance: - Centralized error handling
    Note over Client,Finance: - Consistent state
```

### Option 2: Finance-Orchestrated Registration

This approach makes the Finance service the orchestrator, since it owns the business data.

```mermaid
sequenceDiagram
    participant Client as Finance Client
    participant Finance as Finance Service
    participant IdP as Identity Provider
    
    Client->>Finance: 1. Complete Registration Request
    Note over Client,Finance: {email, password, firstName, lastName, company, editionCode}
    
    Finance->>Finance: 2. Create Organization
    Finance->>Finance: Store Organization
    
    Finance->>Finance: 3. Create User
    Finance->>Finance: Store User with fkOrganizationId
    
    Finance->>IdP: 4. Create Identity
    Note over Finance,IdP: clientId = User._id
    IdP->>IdP: Store Identity
    IdP-->>Finance: Return tokens
    
    Finance-->>Client: 5. Return complete response
    Note over Finance,Client: {user, tokens, organization}
```

### Option 3: Event-Driven Registration

This approach uses events to decouple the services while maintaining consistency.

```mermaid
sequenceDiagram
    participant Client as Finance Client
    participant Finance as Finance Service
    participant IdP as Identity Provider
    participant EventBus as Event Bus
    
    Client->>Finance: 1. Registration Request
    Finance->>Finance: 2. Create Organization & User
    Finance->>EventBus: 3. Publish UserCreated Event
    EventBus->>IdP: 4. UserCreated Event
    IdP->>IdP: 5. Create Identity
    IdP->>EventBus: 6. Publish IdentityCreated Event
    EventBus->>Finance: 7. IdentityCreated Event
    Finance->>Finance: 8. Update User with Identity info
    Finance-->>Client: 9. Return response
```

## Detailed Implementation Proposals

### Recommended: IdP-Orchestrated Registration

#### New IdP Registration Endpoint

```typescript
// POST /api/v1/auth/register-complete
interface CompleteRegistrationDTO {
  // Identity fields
  email: string;
  password: string;
  clientOrigin: EnumClientOrigin;
  
  // User business fields
  firstName: string;
  lastName: string;
  preferredName?: string;
  cellNumber: string;
  
  // Organization fields
  organizationName: string;
  editionCode: EnumEditions;
  userTypeCode: EnumUserTypes;
}

interface CompleteRegistrationResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    organization: {
      id: string;
      name: string;
      editionCode: EnumEditions;
    };
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}
```

#### IdP Registration Service Implementation

```typescript
class CompleteRegistrationService {
  async registerComplete(dto: CompleteRegistrationDTO): Promise<CompleteRegistrationResponse> {
    // Start transaction
    const session = await mongoose.startSession();
    session.startTransaction();
    
    try {
      // 1. Create organization in Finance service
      const organization = await this.financeService.createOrganization({
        organizationName: dto.organizationName,
        editionCode: dto.editionCode
      });
      
      // 2. Create user in Finance service
      const user = await this.financeService.createUser({
        fkOrganizationId: organization.id,
        userTypeCode: dto.userTypeCode,
        editionCode: dto.editionCode,
        firstName: dto.firstName,
        lastName: dto.lastName,
        preferredName: dto.preferredName,
        email: dto.email,
        cellNumber: dto.cellNumber,
        isActive: true
      });
      
      // 3. Create identity record
      const identity = await this.identityRepository.create({
        clientId: user.id, // Finance User._id
        clientOrigin: dto.clientOrigin,
        clientPath: EnumClientPath.Finance,
        email: dto.email,
        password: await this.hashPassword(dto.password),
        isActive: true,
        roles: this.getDefaultRoles(dto.userTypeCode)
      });
      
      // 4. Generate tokens
      const tokens = await this.tokenService.generateTokens(identity);
      
      await session.commitTransaction();
      
      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          organization: {
            id: organization.id,
            name: organization.organizationName,
            editionCode: organization.editionCode
          }
        },
        tokens
      };
      
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
    }
  }
}
```

#### Finance Service API Extensions

The Finance service needs new internal endpoints for the IdP to call:

```typescript
// Internal endpoints (not exposed to clients)
// POST /api/v1/internal/organizations
interface CreateOrganizationRequest {
  organizationName: string;
  editionCode: EnumEditions;
}

// POST /api/v1/internal/users
interface CreateUserRequest {
  fkOrganizationId: string;
  userTypeCode: EnumUserTypes;
  editionCode: EnumEditions;
  firstName: string;
  lastName: string;
  preferredName?: string;
  email: string;
  cellNumber: string;
  isActive: boolean;
}
```

#### Client-Side Implementation

```typescript
// Updated tRPC router
export const identityRouter = createTRPCRouter({
  registerComplete: publicProcedureWithLogging
    .input(completeRegistrationSchema)
    .mutation(async ({ input }) => {
      const response = await fetch(`${IDP_BASE_URL}/auth/register-complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(input)
      });

      if (!response.ok) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Registration failed'
        });
      }

      return response.json();
    })
});
```

## API Flow Comparisons

### Current Flow vs Proposed Flow

#### Current (Problematic)
```mermaid
graph TD
    A[Client Registration Form] --> B[Create Organization API]
    B --> C[Create User API]
    C --> D[Create Identity API]
    D --> E[Login to get tokens]

    F[Failure at any step] --> G[Partial state/cleanup needed]

    style F fill:#ff9999
    style G fill:#ff9999
```

#### Proposed (IdP-Orchestrated)
```mermaid
graph TD
    A[Client Registration Form] --> B[Complete Registration API]
    B --> C[Atomic Transaction]
    C --> D[Success: All data created + tokens]
    C --> E[Failure: Complete rollback]

    style D fill:#99ff99
    style E fill:#ffff99
```

## Service Communication Patterns

### Current Authentication Pattern

```mermaid
graph LR
    A[Client] -->|JWT Token| B[Finance Service]
    B -->|Token Introspection| C[IdP Service]
    C -->|Validation Response| B
    B -->|Business Data| A

    style B fill:#e1f5fe
    style C fill:#f3e5f5
```

### Proposed Registration Pattern

```mermaid
graph LR
    A[Client] -->|Registration Data| B[IdP Service]
    B -->|Create Org| C[Finance Service]
    B -->|Create User| C
    C -->|User/Org Data| B
    B -->|Complete Response| A

    style B fill:#f3e5f5
    style C fill:#e1f5fe
```

## Error Handling Strategies

### Current Issues
- **Partial Failures**: Organization created but user creation fails
- **Orphaned Data**: User created but identity registration fails
- **Complex Cleanup**: Manual rollback of multiple services
- **Inconsistent State**: Different services have different data

### Proposed Solutions

#### 1. Distributed Transactions (Recommended)
```typescript
// Using database transactions across services
class RegistrationOrchestrator {
  async registerWithTransaction(dto: CompleteRegistrationDTO) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const org = await this.createOrganization(dto, session);
      const user = await this.createUser(dto, org.id, session);
      const identity = await this.createIdentity(dto, user.id, session);

      await session.commitTransaction();
      return { user, identity, tokens };
    } catch (error) {
      await session.abortTransaction();
      throw error;
    }
  }
}
```

#### 2. Saga Pattern (Alternative)
```typescript
// Compensating actions for each step
class RegistrationSaga {
  async execute(dto: CompleteRegistrationDTO) {
    const compensations = [];

    try {
      const org = await this.createOrganization(dto);
      compensations.push(() => this.deleteOrganization(org.id));

      const user = await this.createUser(dto, org.id);
      compensations.push(() => this.deleteUser(user.id));

      const identity = await this.createIdentity(dto, user.id);

      return { user, identity };
    } catch (error) {
      // Execute compensations in reverse order
      for (const compensate of compensations.reverse()) {
        await compensate();
      }
      throw error;
    }
  }
}
```

## Security Considerations

### Current Security Issues
1. **Multiple Attack Surfaces**: Each service endpoint is a potential attack vector
2. **Credential Exposure**: Password sent to multiple services
3. **Partial Registration**: Attackers could create partial accounts

### Proposed Security Improvements

#### 1. Single Registration Endpoint
- Only IdP handles sensitive credentials
- Reduced attack surface
- Centralized security controls

#### 2. Service-to-Service Authentication
```typescript
// Internal API calls with service tokens
class FinanceServiceClient {
  private serviceToken: string;

  async createUser(userData: CreateUserRequest) {
    return fetch('/internal/users', {
      headers: {
        'Authorization': `Service ${this.serviceToken}`,
        'X-Service-Name': 'identity-provider'
      },
      body: JSON.stringify(userData)
    });
  }
}
```

#### 3. Input Validation & Sanitization
```typescript
const completeRegistrationSchema = z.object({
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/),
  firstName: z.string().min(1).max(100).regex(/^[a-zA-Z\s'-]+$/),
  lastName: z.string().min(1).max(100).regex(/^[a-zA-Z\s'-]+$/),
  organizationName: z.string().min(1).max(200),
  cellNumber: z.string().regex(/^\+?[1-9]\d{1,14}$/),
  editionCode: z.nativeEnum(EnumEditions),
  userTypeCode: z.nativeEnum(EnumUserTypes)
});
```

## Implementation Recommendations

### Phase 1: Immediate Improvements (Recommended)

1. **Implement IdP-Orchestrated Registration**
   - Create `/auth/register-complete` endpoint in IdP
   - Add internal endpoints to Finance service
   - Update Client tRPC router
   - Implement distributed transaction handling

2. **Service-to-Service Authentication**
   - Generate service tokens for internal API calls
   - Add authentication middleware for internal endpoints
   - Implement request validation and rate limiting

3. **Enhanced Error Handling**
   - Implement transaction rollback mechanisms
   - Add comprehensive logging and monitoring
   - Create error recovery procedures

### Phase 2: Advanced Features

1. **Event-Driven Architecture**
   - Implement event bus (Redis/RabbitMQ)
   - Add event sourcing for audit trails
   - Implement eventual consistency patterns

2. **Advanced Security**
   - Add request signing for service-to-service calls
   - Implement API rate limiting and throttling
   - Add comprehensive audit logging

## Migration Strategy

### Step 1: Backward Compatibility
```typescript
// Support both old and new registration flows
export const identityRouter = createTRPCRouter({
  // New recommended endpoint
  registerComplete: publicProcedure
    .input(completeRegistrationSchema)
    .mutation(async ({ input }) => {
      return await completeRegistrationService.register(input);
    }),

  // Legacy endpoints (deprecated)
  register: publicProcedure
    .input(legacyRegistrationSchema)
    .mutation(async ({ input }) => {
      console.warn('Using deprecated registration endpoint');
      return await legacyRegistrationService.register(input);
    })
});
```

### Step 2: Gradual Migration
1. Deploy new endpoints alongside existing ones
2. Update client to use new endpoints for new registrations
3. Migrate existing partial registrations
4. Remove legacy endpoints after full migration

### Step 3: Data Consistency Validation
```typescript
// Validation script to ensure data consistency
class DataConsistencyValidator {
  async validateRegistrations() {
    const users = await financeService.getAllUsers();
    const identities = await idpService.getAllIdentities();

    for (const user of users) {
      const identity = identities.find(i => i.clientId === user._id);
      if (!identity) {
        console.error(`Orphaned user: ${user.email}`);
      }
    }

    for (const identity of identities) {
      const user = users.find(u => u._id === identity.clientId);
      if (!user) {
        console.error(`Orphaned identity: ${identity.email}`);
      }
    }
  }
}
```

## Performance Considerations

### Current Performance Issues
- **Multiple Round Trips**: 3+ API calls for registration
- **Network Latency**: Each call adds latency
- **Resource Contention**: Multiple database connections

### Proposed Performance Improvements

#### 1. Single API Call
- Reduces network round trips from 3+ to 1
- Eliminates client-side coordination overhead
- Reduces total registration time by 60-80%

#### 2. Connection Pooling
```typescript
// Optimized database connections
class DatabaseManager {
  private financePool: ConnectionPool;
  private idpPool: ConnectionPool;

  async executeInTransaction(operations: Operation[]) {
    const connections = await Promise.all([
      this.financePool.getConnection(),
      this.idpPool.getConnection()
    ]);

    try {
      await Promise.all(operations.map(op => op.execute()));
    } finally {
      connections.forEach(conn => conn.release());
    }
  }
}
```

#### 3. Caching Strategy
```typescript
// Cache organization and user type data
class RegistrationCache {
  private cache = new Map();

  async getOrganizationDefaults(editionCode: EnumEditions) {
    const key = `org-defaults-${editionCode}`;
    if (!this.cache.has(key)) {
      const defaults = await this.fetchOrganizationDefaults(editionCode);
      this.cache.set(key, defaults);
    }
    return this.cache.get(key);
  }
}
```

## Monitoring and Observability

### Key Metrics to Track
1. **Registration Success Rate**: % of successful complete registrations
2. **Registration Latency**: Time from start to completion
3. **Partial Registration Rate**: % of incomplete registrations
4. **Service Availability**: Uptime of each service
5. **Error Rates**: By service and error type

### Logging Strategy
```typescript
// Structured logging for registration flow
class RegistrationLogger {
  logRegistrationStart(correlationId: string, email: string) {
    logger.info('Registration started', {
      correlationId,
      email: this.maskEmail(email),
      timestamp: new Date().toISOString(),
      service: 'identity-provider'
    });
  }

  logRegistrationStep(correlationId: string, step: string, duration: number) {
    logger.info('Registration step completed', {
      correlationId,
      step,
      duration,
      timestamp: new Date().toISOString()
    });
  }
}
```

## Conclusion

The current registration flow has significant architectural issues that create complexity, security risks, and poor user experience. The **IdP-Orchestrated Registration** approach is the recommended solution because it:

1. **Simplifies Client Logic**: Single API call instead of complex orchestration
2. **Ensures Data Consistency**: Atomic transactions prevent partial states
3. **Improves Security**: Centralized credential handling and reduced attack surface
4. **Enhances Performance**: Eliminates multiple round trips and reduces latency
5. **Enables Better Error Handling**: Centralized error management and recovery

The migration can be done gradually with backward compatibility, ensuring zero downtime and smooth transition for existing users.

### Next Steps
1. Review and approve the proposed architecture
2. Create detailed implementation tickets
3. Set up development environment for testing
4. Implement Phase 1 improvements
5. Plan migration timeline for existing data
