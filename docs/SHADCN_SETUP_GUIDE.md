# Shadcn/ui Setup and Configuration Guide

This guide covers the complete setup and usage of Shadcn/ui components in the Excelytics Finance Client project.

## Table of Contents
- [Initial Setup](#initial-setup)
- [Configuration](#configuration)
- [Adding Components](#adding-components)
- [Component Usage](#component-usage)
- [Customization](#customization)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Initial Setup

### 1. Prerequisites
Ensure you have the following installed:
- Bun (package manager)
- React 19+
- TypeScript
- Tailwind CSS

### 2. Configuration File
Create `components.json` in the project root:

```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "client/src/index.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "client/src/components",
    "utils": "client/src/lib/utils",
    "ui": "client/src/components/ui"
  }
}
```

### 3. Required Dependencies
Install the core dependencies:

```bash
bun add @radix-ui/react-slot class-variance-authority clsx tailwind-merge
bun add lucide-react  # For icons
```

### 4. Utility Function
Ensure `client/src/lib/utils.ts` exists:

```typescript
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

## Configuration

### Tailwind CSS Setup
Your `client/src/index.css` should include:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    /* ... other dark mode variables */
  }
}
```

### TypeScript Configuration
Ensure your `tsconfig.json` includes path mapping:

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["client/src/*"]
    }
  }
}
```

## Adding Components

### Using the CLI
Add components using the Shadcn CLI:

```bash
# Add individual components
bunx shadcn@latest add button
bunx shadcn@latest add card
bunx shadcn@latest add chart

# Add multiple components
bunx shadcn@latest add button card input label
```

### Available Components
Common components you can add:

- **Layout**: `card`, `separator`, `sheet`
- **Forms**: `button`, `input`, `label`, `textarea`, `select`
- **Data Display**: `table`, `badge`, `avatar`
- **Feedback**: `alert`, `toast`, `dialog`
- **Charts**: `chart` (includes Recharts integration)
- **Navigation**: `tabs`, `dropdown-menu`, `navigation-menu`

### Manual Installation
If CLI fails, manually create components in `client/src/components/ui/`:

```typescript
// Example: client/src/components/ui/button.tsx
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
```

## Component Usage

### Basic Usage
```typescript
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Card</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="default">Click me</Button>
        <Button variant="outline">Outline</Button>
        <Button variant="ghost" size="sm">Small Ghost</Button>
      </CardContent>
    </Card>
  )
}
```

### Chart Components
```typescript
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { LineChart, Line, XAxis, YAxis } from "recharts"

const chartConfig = {
  usage: {
    label: "Usage",
    color: "hsl(var(--chart-1))",
  },
}

function UsageChart({ data }) {
  return (
    <ChartContainer config={chartConfig} className="h-[300px]">
      <LineChart data={data}>
        <XAxis dataKey="date" />
        <YAxis />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Line dataKey="usage" stroke="var(--color-usage)" />
      </LineChart>
    </ChartContainer>
  )
}
```

### Form Components
```typescript
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

function LoginForm() {
  return (
    <form className="space-y-4">
      <div>
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" placeholder="Enter your email" />
      </div>
      <div>
        <Label htmlFor="password">Password</Label>
        <Input id="password" type="password" />
      </div>
      <Button type="submit" className="w-full">
        Sign In
      </Button>
    </form>
  )
}
```

## Customization

### Theme Customization
Modify CSS variables in `client/src/index.css`:

```css
:root {
  --primary: 210 100% 50%;  /* Custom blue */
  --secondary: 120 100% 50%; /* Custom green */
  --radius: 0.75rem;        /* Larger border radius */
}
```

### Component Variants
Extend component variants:

```typescript
const buttonVariants = cva(
  // base classes
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        // Add custom variant
        gradient: "bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600",
      },
      size: {
        default: "h-10 py-2 px-4",
        // Add custom size
        xl: "h-14 px-10 text-lg",
      },
    },
  }
)
```

### Dark Mode Support
Components automatically support dark mode through CSS variables. Toggle with:

```typescript
// Add to your theme provider or root component
function ThemeToggle() {
  const [isDark, setIsDark] = useState(false)
  
  useEffect(() => {
    document.documentElement.classList.toggle('dark', isDark)
  }, [isDark])
  
  return (
    <Button onClick={() => setIsDark(!isDark)}>
      Toggle Theme
    </Button>
  )
}
```

## Best Practices

### 1. Import Organization
```typescript
// Group imports logically
import React from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap, TrendingUp } from 'lucide-react'
```

### 2. Component Composition
```typescript
// Create composite components
function StatCard({ title, value, icon: Icon, trend }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{trend}</p>
      </CardContent>
    </Card>
  )
}
```

### 3. Consistent Spacing
Use Tailwind's spacing scale consistently:
- `space-y-4` for vertical spacing
- `gap-4` for grid/flex gaps
- `p-4`, `px-6`, `py-2` for padding

### 4. Responsive Design
```typescript
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
  {/* Cards automatically responsive */}
</div>
```

## Troubleshooting

### Common Issues

1. **Import Path Errors**
   ```typescript
   // Wrong
   import { cn } from "client/lib/utils"
   
   // Correct
   import { cn } from "@/lib/utils"
   ```

2. **Missing CSS Variables**
   - Ensure all CSS variables are defined in `index.css`
   - Check for typos in variable names

3. **Component Not Found**
   ```bash
   # Reinstall component
   bunx shadcn@latest add button --overwrite
   ```

4. **Styling Issues**
   - Verify Tailwind CSS is properly configured
   - Check for conflicting CSS classes
   - Ensure `cn()` utility is used for conditional classes

### Debugging Tips

1. **Check Component Structure**
   ```bash
   ls client/src/components/ui/
   ```

2. **Verify Imports**
   ```typescript
   // Test import in browser console
   console.log(Button) // Should not be undefined
   ```

3. **CSS Variable Inspection**
   ```css
   /* Check in browser dev tools */
   :root {
     /* Verify all variables are present */
   }
   ```

## Next Steps

1. **Add More Components**: Gradually add components as needed
2. **Create Custom Components**: Build project-specific components using Shadcn as base
3. **Theme System**: Implement comprehensive theming
4. **Component Library**: Document your custom components
5. **Testing**: Add tests for custom component variants

---

For more information, visit the [official Shadcn/ui documentation](https://ui.shadcn.com/).
