> dated: 09.06.2025

This is an excellent starting point, <PERSON><PERSON>. You've got all the core configuration files in place, and your thinking is spot on. Using Bun for everything—development server, bundling, and running scripts—is a fantastic, modern approach that will keep your client-side setup lean and fast.

Let's structure this setup logically, addressing the key responsibilities of the Client project:
1.  **Authentication:** Handling login/registration with the `IdP`.
2.  **Core Functionality:** Uploading files to the `Calc` service and displaying results.
3.  **Data Display:** Getting and showing data from the `Finance` service.

Here is a complete, step-by-step guide to flesh out your client-side architecture, building upon the great foundation you've already laid.

---

### 1. Project Structure and Configuration (Refinements)

Your file structure is good. Let's refine it slightly for clarity and add the necessary pages and components.

**Refined `src` Directory Structure:**

```
src/
├── api/
│   ├── auth.ts         # Functions to call the IdP (login, register)
│   ├── calc.ts         # Functions to call the Calc service (upload)
│   └── finance.ts      # Functions to call the Finance service (get data)
├── assets/
│   └── react.svg
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── MainLayout.tsx # Wrapper with header/footer
│   ├── ui/               # Your Shadcn/ui components will go here
│   │   ├── button.tsx
│   │   └── ...
│   └── FileUpload.tsx
├── config/
│   ├── build.ts
│   └── server.ts
├── contexts/
│   └── AuthContext.tsx   # Manages user state and JWT
├── hooks/
│   └── useAuth.ts        # Simple hook to access AuthContext
├── lib/
│   ├── apiClient.ts      # Your configured Axios instance
│   └── utils.ts          # Shadcn/ui utility functions
├── pages/
│   ├── Auth/
│   │   ├── LoginPage.tsx
│   │   └── RegisterPage.tsx
│   ├── DashboardPage.tsx
│   ├── HomePage.tsx
│   └── NotFoundPage.tsx
├── routes/
│   ├── AppRoutes.tsx     # Defines all application routes
│   └── ProtectedRoute.tsx  # Wrapper for routes that require authentication
├── App.tsx               # Main app component, sets up router
├── main.tsx              # Entry point, renders App
└── index.css             # Global styles
```

---

### 2. Authentication Flow (`AuthContext` and API Calls)

This is the most critical piece. We need a context to manage the user's authentication state and JWT token globally.

**`src/contexts/AuthContext.tsx`**
```typescript
import {
	createContext,
	useState,
	useEffect,
	type ReactNode,
	useCallback,
} from 'react';
import { login as apiLogin, register as apiRegister } from '@/api/auth';
import type { LoginDTO, RegisterDTO } from 'excelytics.shared-models'; // Assuming these exist

interface AuthContextType {
	isAuthenticated: boolean;
	user: object | null; // Define a proper user type later
	login: (credentials: LoginDTO) => Promise<void>;
	register: (details: RegisterDTO) => Promise<void>;
	logout: () => void;
	isLoading: boolean;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
	const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
	const [user, setUser] = useState<object | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(true);

	// Check for a token in localStorage on initial load
	useEffect(() => {
		const token = localStorage.getItem('authToken');
		if (token) {
			// Here you would typically decode the token to get user info
			// and check its expiry. For now, we'll just set authenticated.
			setIsAuthenticated(true);
			// setUser(decodedUser);
		}
		setIsLoading(false);
	}, []);

	const login = useCallback(async (credentials: LoginDTO) => {
		const { token, user } = await apiLogin(credentials);
		localStorage.setItem('authToken', token);
		setIsAuthenticated(true);
		setUser(user);
	}, []);

	const register = useCallback(async (details: RegisterDTO) => {
		const { token, user } = await apiRegister(details);
		localStorage.setItem('authToken', token);
		setIsAuthenticated(true);
		setUser(user);
	}, []);

	const logout = useCallback(() => {
		localStorage.removeItem('authToken');
		setIsAuthenticated(false);
		setUser(null);
	}, []);

	return (
		<AuthContext.Provider
			value={{ isAuthenticated, user, login, register, logout, isLoading }}
		>
			{children}
		</AuthContext.Provider>
	);
};
```

**`src/api/auth.ts`** (Example API calls)
```typescript
import apiClient from '@/lib/apiClient';
import type { LoginDTO, RegisterDTO } from 'excelytics.shared-models';

// The base URL for your IdP service should be in an environment variable
const IDP_API_URL = Bun.env.IDP_API_URL || 'http://localhost:6060/api/v1';

export const login = async (credentials: LoginDTO) => {
	const response = await apiClient.post(
		`${IDP_API_URL}/auth/login`,
		credentials,
	);
	return response.data; // Assuming the API returns { token, user }
};

export const register = async (details: RegisterDTO) => {
	const response = await apiClient.post(
		`${IDP_API_URL}/auth/register`,
		details,
	);
	return response.data;
};
```

---

### 3. Routing (`react-router-dom`)

We need to define public routes (login, register) and protected routes (dashboard).

**`src/routes/ProtectedRoute.tsx`**
```typescript
import { Navigate, Outlet } from 'react-router-dom';
import useAuth from '@/hooks/useAuth'; // A simple hook to get AuthContext

const ProtectedRoute = () => {
	const { isAuthenticated, isLoading } = useAuth();

	if (isLoading) {
		return <div>Loading...</div>; // Or a spinner component
	}

	return isAuthenticated ? <Outlet /> : <Navigate to="/login" replace />;
};

export default ProtectedRoute;
```

**`src/routes/AppRoutes.tsx`**
```typescript
import { Routes, Route } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import MainLayout from '@/components/layout/MainLayout';
import HomePage from '@/pages/HomePage';
import LoginPage from '@/pages/Auth/LoginPage';
import RegisterPage from '@/pages/Auth/RegisterPage';
import DashboardPage from '@/pages/DashboardPage';
import NotFoundPage from '@/pages/NotFoundPage';

const AppRoutes = () => {
	return (
		<Routes>
			<Route element={<MainLayout />}>
				{/* Public Routes */}
				<Route path="/" element={<HomePage />} />
				<Route path="/login" element={<LoginPage />} />
				<Route path="/register" element={<RegisterPage />} />

				{/* Protected Routes */}
				<Route element={<ProtectedRoute />}>
					<Route path="/dashboard" element={<DashboardPage />} />
				</Route>

				{/* Catch-all Not Found Route */}
				<Route path="*" element={<NotFoundPage />} />
			</Route>
		</Routes>
	);
};

export default AppRoutes;
```

**`src/App.tsx`**
```typescript
import { BrowserRouter as Router } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import AppRoutes from './routes/AppRoutes';

function App() {
	return (
		<AuthProvider>
			<Router>
				<AppRoutes />
			</Router>
		</AuthProvider>
	);
}

export default App;
```

---

### 4. Environment Variables

Your client needs to know the URLs of your backend services. Create a `.env` file in the root of your `Client` project.

**`.env`**
```
# The port for the Bun development server
PORT=3000

# Base URLs for your backend microservices
# These should point to your Nginx gateway in production
IDP_API_URL=http://localhost:6060/api/v1
FINANCE_API_URL=http://localhost:6000/api/v1
CALC_API_URL=http://localhost:8080/api/v1
```

Bun automatically loads environment variables from `.env`, `.env.local`, etc., making them available as `Bun.env.VARIABLE_NAME`.

### Summary of Responsibilities

*   **Client:** Renders the UI, manages UI state.
*   **`IdP` (Identity Provider):** Handles user creation, login, and issuing JWTs. The Client calls its `/login` and `/register` endpoints.
*   **`Calc` (Calculation Engine):** Handles the heavy lifting of processing Excel files. The Client will have a `FileUpload` component that sends the file to an endpoint like `/upload` on the `Calc` service.
*   **`Finance` (Main Backend):** Acts as the primary data source after login. The Client will make authenticated requests (attaching the JWT from the `IdP`) to the `Finance` service to get the data needed to populate the charts and tables on the dashboard.

This setup provides a clean separation of concerns and a solid, scalable foundation for your SaaS application's frontend. 
