# Integration Testing Guide - tRPC with Identity Service

This guide covers the complete integration testing setup for the tRPC API layer that communicates with the Introspection.Identity microservice.

## Overview

The integration tests verify end-to-end functionality between the Finance Client's tRPC layer and the actual Identity service, ensuring:

- **Type Safety**: tRPC types match actual service responses
- **Error Handling**: Proper error propagation and handling
- **Performance**: Response times meet requirements
- **Reliability**: Service handles concurrent requests
- **Validation**: Input validation works correctly

## Architecture

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Integration       │    │   tRPC Server       │    │   Identity Service  │
│   Tests             │    │   (Finance Client)  │    │   (Microservice)    │
│                     │    │                     │    │                     │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │    │ ┌─────────────────┐ │
│ │ Test Runner     │ │◄──►│ │ Identity Router │ │◄──►│ │ Auth Endpoints  │ │
│ │                 │ │    │ │                 │ │    │ │                 │ │
│ │ - Health Tests  │ │    │ │ - login()       │ │    │ │ POST /login     │ │
│ │ - Login Tests   │ │    │ │ - register()    │ │    │ │ POST /register  │ │
│ │ - Reg Tests     │ │    │ │ - healthCheck() │ │    │ │ GET /health     │ │
│ └─────────────────┘ │    │ └─────────────────┘ │    │ │ GET /health/all │ │
│                     │    │                     │    │ └─────────────────┘ │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │    │                     │
│ │ Test Helpers    │ │    │ │ HTTP Client     │ │    │ ┌─────────────────┐ │
│ │                 │ │    │ │                 │ │    │ │ Database        │ │
│ │ - User Creation │ │    │ │ - Validation    │ │    │ │ - Users         │ │
│ │ - Cleanup       │ │    │ │ - Auth Headers  │ │    │ │ - Sessions      │ │
│ │ - Performance   │ │    │ │ - Error Format  │ │    │ │ - Tokens        │ │
│ └─────────────────┘ │    │ └─────────────────┘ │    │ └─────────────────┘ │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Test Files Structure

### Core Test Files
- **`identity-health.test.ts`**: Health check and service availability tests
- **`identity-login.test.ts`**: User authentication and login flow tests  
- **`identity-registration.test.ts`**: User registration and validation tests

### Support Files
- **`config/test.config.ts`**: Test configuration and constants
- **`helpers/trpc-test.helper.ts`**: Test utilities and helper functions
- **`setup.ts`**: Global test setup and initialization
- **`run-integration-tests.ts`**: Comprehensive test runner with reporting

## Test Categories

### 1. Health Check Tests
**Purpose**: Verify service availability and monitoring endpoints

**Test Cases**:
- Basic health check via tRPC
- Direct service health check
- Deep health check (`/health/all`)
- Performance under load
- Concurrent health checks
- Service unavailability handling

**Key Validations**:
```typescript
expect(result.status).toBe('healthy');
expect(result.service).toBe('identity');
expect(result.timestamp).toBeDefined();
expect(responseTime).toBeLessThan(1000); // 1 second max
```

### 2. Registration Tests
**Purpose**: Verify user creation and input validation

**Test Cases**:
- Successful user registration
- Duplicate email prevention
- Email format validation
- Password strength requirements
- Required field validation
- Optional field handling
- Special character support
- Concurrent registrations

**Key Validations**:
```typescript
expect(result.id).toBeDefined();
expect(result.email).toBe(testUser.email);
expect(result.emailVerified).toBe(false);
expect(responseTime).toBeLessThan(5000); // 5 seconds max
```

### 3. Login Tests
**Purpose**: Verify authentication and token generation

**Test Cases**:
- Successful login with valid credentials
- Invalid password rejection
- Non-existent email handling
- Email format validation
- Remember me functionality
- Token structure validation
- Concurrent login handling

**Key Validations**:
```typescript
expect(result.user.email).toBe(testUser.email);
expect(result.tokens.accessToken).toBeDefined();
expect(result.tokens.refreshToken).toBeDefined();
expect(result.tokens.expiresIn).toBeGreaterThan(0);
expect(responseTime).toBeLessThan(3000); // 3 seconds max
```

## Running Tests

### Prerequisites
1. **Identity Service Running**:
   ```bash
   # Start Identity service (from Identity repo)
   cd ../Introspection.Identity
   bun run dev
   
   # Verify service is running
   curl http://localhost:3002/api/v1/health
   ```

2. **Finance Client tRPC Server**:
   ```bash
   # Start Finance Client server
   bun run dev
   
   # Verify tRPC endpoint
   curl http://localhost:4200/api/trpc/identity.healthCheck
   ```

### Test Execution

#### Quick Test Commands
```bash
# Run all integration tests
bun run test:integration

# Run specific test suites
bun run test:identity:health
bun run test:identity:login  
bun run test:identity:registration

# Run with watch mode
bun run test:watch
```

#### Comprehensive Test Runner
```bash
# Run with detailed reporting and service checks
bun run test:integration:run
```

This provides:
- Service availability checking
- Detailed test reporting
- Performance analysis
- Error summaries
- Recommendations

#### Individual Test Files
```bash
# Run specific test file
bun test tests/integration/identity-login.test.ts

# Run with verbose output
DEBUG=* bun test tests/integration/identity-health.test.ts
```

## Test Configuration

### Environment Variables
```bash
# Optional - defaults to localhost:3002
export IDENTITY_SERVICE_URL=http://localhost:3002

# Test environment
export NODE_ENV=test
```

### Performance Thresholds
```typescript
PERFORMANCE: {
  HEALTH_CHECK_MAX_TIME: 1000,    // 1 second
  LOGIN_MAX_TIME: 3000,           // 3 seconds  
  REGISTRATION_MAX_TIME: 5000,    // 5 seconds
}
```

### Test Data Configuration
```typescript
TEST_DATA: {
  CLIENT_ORIGIN: 1,                    // EnumClientOrigin.Excelytics
  VALID_PASSWORD: 'TestPassword@123',
  VALID_EMAIL_DOMAIN: '@excelytics.com',
}
```

## Expected Results

### Successful Test Run
```
🚀 Starting tRPC Integration Test Suite
============================================================

🔍 Checking Identity service availability...
✅ Identity service is available for testing

🧪 Running Health Checks Tests
----------------------------------------
✅ Health Checks: 8 tests passed in 1247ms

🧪 Running User Registration Tests  
----------------------------------------
✅ User Registration: 12 tests passed in 3891ms

🧪 Running User Login Tests
----------------------------------------
✅ User Login: 10 tests passed in 2156ms

============================================================
📊 INTEGRATION TEST REPORT
============================================================

📈 Summary:
   Total Tests: 30
   Passed: 30 ✅
   Failed: 0 ✅
   Duration: 7294ms
   Success Rate: 100.0%

⚡ Performance Analysis:
   Health Checks: 156ms avg per test
   User Registration: 324ms avg per test
   User Login: 216ms avg per test

💡 Recommendations:
   • All tests passed! Consider adding more edge cases
   • Monitor performance trends over time

✅ All integration tests passed successfully!
```

## Troubleshooting

### Common Issues

1. **Service Not Available**
   ```
   ❌ Cannot run integration tests - Identity service is not available
   ```
   **Solutions**:
   - Start Identity service: `cd ../Introspection.Identity && bun run dev`
   - Check service health: `curl http://localhost:3002/api/v1/health`
   - Verify port 3002 is not blocked

2. **tRPC Connection Issues**
   ```
   Error: Failed to fetch from tRPC endpoint
   ```
   **Solutions**:
   - Start Finance Client server: `bun run dev`
   - Verify tRPC endpoint: `curl http://localhost:4200/api/trpc`
   - Check for port conflicts

3. **Test Timeouts**
   ```
   Error: Test timeout exceeded
   ```
   **Solutions**:
   - Check service performance
   - Increase timeout values in `test.config.ts`
   - Monitor system resources

4. **Authentication Errors**
   ```
   Error: User creation failed
   ```
   **Solutions**:
   - Check Identity service logs
   - Verify database connectivity
   - Ensure test user cleanup is working

### Debugging Steps

1. **Check Service Status**:
   ```bash
   # Identity service health
   curl -v http://localhost:3002/api/v1/health
   curl -v http://localhost:3002/api/v1/health/all
   
   # tRPC health check
   curl -v http://localhost:4200/api/trpc/identity.healthCheck
   ```

2. **Run Individual Tests**:
   ```bash
   # Test just health checks
   bun run test:identity:health
   
   # Test with debug output
   DEBUG=* bun test tests/integration/identity-login.test.ts
   ```

3. **Monitor Service Logs**:
   - Check Identity service console output
   - Monitor database connection status
   - Watch for authentication errors

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    services:
      identity:
        image: introspection/identity:latest
        ports:
          - 3002:3002
        env:
          NODE_ENV: test
          DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        
      - name: Install dependencies
        run: bun install
        
      - name: Wait for services
        run: |
          timeout 60 bash -c 'until curl -f http://localhost:3002/api/v1/health; do sleep 2; done'
        
      - name: Run integration tests
        run: bun run test:integration:run
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/
```

## Best Practices

### Test Design
- **Isolation**: Each test should be independent
- **Cleanup**: Always clean up test data
- **Performance**: Assert response time requirements
- **Error Cases**: Test both success and failure scenarios
- **Concurrency**: Test concurrent operations

### Maintenance
- **Regular Updates**: Keep tests in sync with service changes
- **Performance Monitoring**: Track response time trends
- **Error Analysis**: Review and categorize test failures
- **Documentation**: Keep test documentation current

### Security
- **Test Data**: Use only test data, never production data
- **Credentials**: Use secure test passwords and cleanup tokens
- **Isolation**: Ensure tests don't affect production systems

---

This integration testing setup provides comprehensive validation of the tRPC communication layer with the Identity service, ensuring reliability, performance, and type safety across service boundaries.
