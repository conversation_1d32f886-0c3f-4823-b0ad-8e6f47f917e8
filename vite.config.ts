import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
	plugins: [react()],
	root: 'client',
	publicDir: 'public',
	build: {
		outDir: 'dist',
		emptyOutDir: true
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, 'client/src'),
			'@app-types': resolve(__dirname, 'client/types'),
			'@server-trpc-types': resolve(__dirname, 'server/trpc/root.ts')
		}
	},
	define: {
		'import.meta.env.ENV': JSON.stringify(process.env.ENV || 'development'),
		'import.meta.env.PORT': JSON.stringify(process.env.PORT || '4200'),
		'import.meta.env.IDP_SERVICE_URL': JSON.stringify(process.env.IDP_SERVICE_URL || 'http://localhost:6002'),
		'import.meta.env.CALC_SERVICE_URL': JSON.stringify(process.env.CALC_SERVICE_URL || 'http://localhost:6003'),
		'import.meta.env.FINANCE_SERVICE_URL': JSON.stringify(
			process.env.FINANCE_SERVICE_URL || 'http://localhost:6001'
		)
	},
	server: {
		port: 4200,
		host: true
	}
});