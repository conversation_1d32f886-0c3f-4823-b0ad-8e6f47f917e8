// 1. Define base type for your actual KWh data (meter readings)
export interface ActualKwhDataItem {
	date: string;
	kwh: number; // This represents the meter reading (cumulative)
	day: string;
}

// 2. Define type for daily usage calculated from meter readings
export interface DailyUsageDataItem {
	date: string;
	usage: number; // This represents daily consumption (difference between readings)
	day: string;
	meterReading?: number; // Optional reference to the actual meter reading
}

// 3. Define type for the predicted data, making 'confidence' optional
export interface PredictedKwhDataItem {
	date: string;
	day: string;
	predicted: number;
	confidence?: number; // Changed to optional, as per the error message
}

// 4. Define a combined type for all data points to be used in the chart and table
export interface CombinedChartDataItem {
	date: string;
	day: string;
	kwh?: number; // Meter reading
	usage?: number; // Daily usage
	predicted?: number;
	confidence?: number;
}

// 5. Define chart view types
export type ChartViewType = 'trend' | 'usage';
export type ChartDisplayType = 'line' | 'bar' | 'area';