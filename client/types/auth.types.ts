import type { LoginDTO, AccessTokenPayload } from 'excelytics.shared-dtos';

// tRPC-specific register input type
export type TRPCRegisterInput = {
	email: string;
	password: string;
	firstName: string;
	lastName: string;
	company?: string;
};

/**
 * The User object in our context will be the decoded access token payload.
 * We can omit the tokenType as it's always 'ACCESS' in this context.
 */
export type User = Omit<AccessTokenPayload, 'tokenType'>;

/**
 * Represents the shape of the authentication context.
 * This interface defines the properties and methods available in the auth context.
 */
export interface AuthContextType {
	isAuthenticated: boolean;
	token: string | null;
	user: User | null;
	logout: () => void;

	/** Indicates whether the initial authentication check is still in progress. */
	isLoading: boolean;

	/**
	 * Logs in a user with the provided credentials.
	 * @param {LoginDTO} credentials - The login credentials.
	 * @returns {Promise<void>} A promise that resolves when the login is complete.
	 */
	login: (credentials: LoginDTO) => Promise<void>;

	/**
	 * Registers a new user with the provided details.
	 * @param {TRPCRegisterInput} details - The registration details.
	 * @returns {Promise<void>} A promise that resolves when the registration is complete.
	 */
	register: (details: TRPCRegisterInput) => Promise<void>;
}