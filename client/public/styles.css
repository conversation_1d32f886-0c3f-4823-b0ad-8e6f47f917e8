*,
::before,
::after {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x:  ;
	--tw-pan-y:  ;
	--tw-pinch-zoom:  ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position:  ;
	--tw-gradient-via-position:  ;
	--tw-gradient-to-position:  ;
	--tw-ordinal:  ;
	--tw-slashed-zero:  ;
	--tw-numeric-figure:  ;
	--tw-numeric-spacing:  ;
	--tw-numeric-fraction:  ;
	--tw-ring-inset:  ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur:  ;
	--tw-brightness:  ;
	--tw-contrast:  ;
	--tw-grayscale:  ;
	--tw-hue-rotate:  ;
	--tw-invert:  ;
	--tw-saturate:  ;
	--tw-sepia:  ;
	--tw-drop-shadow:  ;
	--tw-backdrop-blur:  ;
	--tw-backdrop-brightness:  ;
	--tw-backdrop-contrast:  ;
	--tw-backdrop-grayscale:  ;
	--tw-backdrop-hue-rotate:  ;
	--tw-backdrop-invert:  ;
	--tw-backdrop-opacity:  ;
	--tw-backdrop-saturate:  ;
	--tw-backdrop-sepia:  ;
	--tw-contain-size:  ;
	--tw-contain-layout:  ;
	--tw-contain-paint:  ;
	--tw-contain-style:  ;
}

::backdrop {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x:  ;
	--tw-pan-y:  ;
	--tw-pinch-zoom:  ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position:  ;
	--tw-gradient-via-position:  ;
	--tw-gradient-to-position:  ;
	--tw-ordinal:  ;
	--tw-slashed-zero:  ;
	--tw-numeric-figure:  ;
	--tw-numeric-spacing:  ;
	--tw-numeric-fraction:  ;
	--tw-ring-inset:  ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur:  ;
	--tw-brightness:  ;
	--tw-contrast:  ;
	--tw-grayscale:  ;
	--tw-hue-rotate:  ;
	--tw-invert:  ;
	--tw-saturate:  ;
	--tw-sepia:  ;
	--tw-drop-shadow:  ;
	--tw-backdrop-blur:  ;
	--tw-backdrop-brightness:  ;
	--tw-backdrop-contrast:  ;
	--tw-backdrop-grayscale:  ;
	--tw-backdrop-hue-rotate:  ;
	--tw-backdrop-invert:  ;
	--tw-backdrop-opacity:  ;
	--tw-backdrop-saturate:  ;
	--tw-backdrop-sepia:  ;
	--tw-contain-size:  ;
	--tw-contain-layout:  ;
	--tw-contain-paint:  ;
	--tw-contain-style:  ;
}

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
	box-sizing: border-box;
	/* 1 */
	border-width: 0;
	/* 2 */
	border-style: solid;
	/* 2 */
	border-color: #e5e7eb;
	/* 2 */
}

::before,
::after {
	--tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
	line-height: 1.5;
	/* 1 */
	-webkit-text-size-adjust: 100%;
	/* 2 */
	-moz-tab-size: 4;
	/* 3 */
	-o-tab-size: 4;
	tab-size: 4;
	/* 3 */
	font-family:
		ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
		'Noto Color Emoji';
	/* 4 */
	font-feature-settings: normal;
	/* 5 */
	font-variation-settings: normal;
	/* 6 */
	-webkit-tap-highlight-color: transparent;
	/* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
	margin: 0;
	/* 1 */
	line-height: inherit;
	/* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
	height: 0;
	/* 1 */
	color: inherit;
	/* 2 */
	border-top-width: 1px;
	/* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: inherit;
	font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
	color: inherit;
	text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
	font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
	font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
	/* 1 */
	font-feature-settings: normal;
	/* 2 */
	font-variation-settings: normal;
	/* 3 */
	font-size: 1em;
	/* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
	font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
	text-indent: 0;
	/* 1 */
	border-color: inherit;
	/* 2 */
	border-collapse: collapse;
	/* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	/* 1 */
	font-feature-settings: inherit;
	/* 1 */
	font-variation-settings: inherit;
	/* 1 */
	font-size: 100%;
	/* 1 */
	font-weight: inherit;
	/* 1 */
	line-height: inherit;
	/* 1 */
	letter-spacing: inherit;
	/* 1 */
	color: inherit;
	/* 1 */
	margin: 0;
	/* 2 */
	padding: 0;
	/* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
	text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
	-webkit-appearance: button;
	/* 1 */
	background-color: transparent;
	/* 2 */
	background-image: none;
	/* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
	outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
	box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
	vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
	-webkit-appearance: textfield;
	/* 1 */
	outline-offset: -2px;
	/* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
	-webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
	-webkit-appearance: button;
	/* 1 */
	font: inherit;
	/* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
	display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
	margin: 0;
}

fieldset {
	margin: 0;
	padding: 0;
}

legend {
	padding: 0;
}

ol,
ul,
menu {
	list-style: none;
	margin: 0;
	padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
	padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
	resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
	opacity: 1;
	/* 1 */
	color: #9ca3af;
	/* 2 */
}

input::placeholder,
textarea::placeholder {
	opacity: 1;
	/* 1 */
	color: #9ca3af;
	/* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role='button'] {
	cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
	cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
	display: block;
	/* 1 */
	vertical-align: middle;
	/* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
	max-width: 100%;
	height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden='until-found'])) {
	display: none;
}

:root {
	--background: 0 0% 100%;
	--foreground: 222.2 84% 4.9%;
	--card: 0 0% 100%;
	--card-foreground: 222.2 84% 4.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 222.2 84% 4.9%;
	--primary: 221.2 83.2% 53.3%;
	--primary-foreground: 210 40% 98%;
	--secondary: 210 40% 96%;
	--secondary-foreground: 222.2 84% 4.9%;
	--muted: 210 40% 96%;
	--muted-foreground: 215.4 16.3% 46.9%;
	--accent: 210 40% 96%;
	--accent-foreground: 222.2 84% 4.9%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 210 40% 98%;
	--border: 214.3 31.8% 91.4%;
	--input: 214.3 31.8% 91.4%;
	--ring: 221.2 83.2% 53.3%;
	--radius: 0.5rem;
	--chart-1: 12 76% 61%;
	--chart-2: 173 58% 39%;
	--chart-3: 197 37% 24%;
	--chart-4: 43 74% 66%;
	--chart-5: 27 87% 67%;
}

.container {
	width: 100%;
}

@media (min-width: 640px) {
	.container {
		max-width: 640px;
	}
}

@media (min-width: 768px) {
	.container {
		max-width: 768px;
	}
}

@media (min-width: 1024px) {
	.container {
		max-width: 1024px;
	}
}

@media (min-width: 1280px) {
	.container {
		max-width: 1280px;
	}
}

@media (min-width: 1536px) {
	.container {
		max-width: 1536px;
	}
}

.relative {
	position: relative;
}

.sticky {
	position: sticky;
}

.top-0 {
	top: 0px;
}

.z-50 {
	z-index: 50;
}

.mx-auto {
	margin-left: auto;
	margin-right: auto;
}

.my-4 {
	margin-top: 1rem;
	margin-bottom: 1rem;
}

.mb-12 {
	margin-bottom: 3rem;
}

.mb-4 {
	margin-bottom: 1rem;
}

.mb-8 {
	margin-bottom: 2rem;
}

.mr-4 {
	margin-right: 1rem;
}

.mr-6 {
	margin-right: 1.5rem;
}

.mt-1 {
	margin-top: 0.25rem;
}

.mt-2 {
	margin-top: 0.5rem;
}

.mt-4 {
	margin-top: 1rem;
}

.block {
	display: block;
}

.flex {
	display: flex;
}

.grid {
	display: grid;
}

.hidden {
	display: none;
}

.h-14 {
	height: 3.5rem;
}

.h-16 {
	height: 4rem;
}

.h-2\.5 {
	height: 0.625rem;
}

.h-4 {
	height: 1rem;
}

.h-6 {
	height: 1.5rem;
}

.h-8 {
	height: 2rem;
}

.min-h-screen {
	min-height: 100vh;
}

.w-4 {
	width: 1rem;
}

.w-6 {
	width: 1.5rem;
}

.w-8 {
	width: 2rem;
}

.w-full {
	width: 100%;
}

.max-w-4xl {
	max-width: 56rem;
}

.max-w-md {
	max-width: 28rem;
}

.max-w-screen-2xl {
	max-width: 1536px;
}

.flex-1 {
	flex: 1 1 0%;
}

.flex-grow {
	flex-grow: 1;
}

.cursor-pointer {
	cursor: pointer;
}

.grid-cols-3 {
	grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-col {
	flex-direction: column;
}

.items-center {
	align-items: center;
}

.justify-end {
	justify-content: flex-end;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.gap-4 {
	gap: 1rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0.5rem * var(--tw-space-x-reverse));
	margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1rem * var(--tw-space-x-reverse));
	margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.rounded {
	border-radius: 0.25rem;
}

.rounded-full {
	border-radius: 9999px;
}

.rounded-lg {
	border-radius: var(--radius);
}

.rounded-md {
	border-radius: calc(var(--radius) - 2px);
}

.rounded-xl {
	border-radius: 0.75rem;
}

.border {
	border-width: 1px;
}

.border-2 {
	border-width: 2px;
}

.border-b {
	border-bottom-width: 1px;
}

.border-t {
	border-top-width: 1px;
}

.border-dashed {
	border-style: dashed;
}

.border-blue-500 {
	--tw-border-opacity: 1;
	border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
	--tw-border-opacity: 1;
	border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.bg-background\/95 {
	background-color: hsl(var(--background) / 0.95);
}

.bg-blue-50 {
	--tw-bg-opacity: 1;
	background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.bg-blue-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.bg-blue-600 {
	--tw-bg-opacity: 1;
	background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-blue-700 {
	--tw-bg-opacity: 1;
	background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.bg-chart-1 {
	background-color: hsl(var(--chart-1));
}

.bg-chart-2 {
	background-color: hsl(var(--chart-2));
}

.bg-chart-3 {
	background-color: hsl(var(--chart-3));
}

.bg-chart-4 {
	background-color: hsl(var(--chart-4));
}

.bg-chart-5 {
	background-color: hsl(var(--chart-5));
}

.bg-gray-200 {
	--tw-bg-opacity: 1;
	background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
	--tw-bg-opacity: 1;
	background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.bg-gray-700 {
	--tw-bg-opacity: 1;
	background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.bg-green-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}

.bg-primary {
	background-color: hsl(var(--primary));
}

.bg-red-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-secondary {
	background-color: hsl(var(--secondary));
}

.bg-white {
	--tw-bg-opacity: 1;
	background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-gradient-to-r {
	background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-500 {
	--tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-purple-600 {
	--tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}

.p-4 {
	padding: 1rem;
}

.p-6 {
	padding: 1.5rem;
}

.p-8 {
	padding: 2rem;
}

.px-3 {
	padding-left: 0.75rem;
	padding-right: 0.75rem;
}

.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}

.px-6 {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}

.py-16 {
	padding-top: 4rem;
	padding-bottom: 4rem;
}

.py-2 {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}

.py-20 {
	padding-top: 5rem;
	padding-bottom: 5rem;
}

.py-3 {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}

.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}

.text-center {
	text-align: center;
}

.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}

.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}

.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}

.text-6xl {
	font-size: 3.75rem;
	line-height: 1;
}

.text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}

.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}

.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}

.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}

.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}

.font-bold {
	font-weight: 700;
}

.font-medium {
	font-weight: 500;
}

.font-semibold {
	font-weight: 600;
}

.text-blue-600 {
	--tw-text-opacity: 1;
	color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
	--tw-text-opacity: 1;
	color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
	--tw-text-opacity: 1;
	color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
	--tw-text-opacity: 1;
	color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
	--tw-text-opacity: 1;
	color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
	--tw-text-opacity: 1;
	color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-muted-foreground {
	color: hsl(var(--muted-foreground));
}

.text-red-500 {
	--tw-text-opacity: 1;
	color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.text-red-600 {
	--tw-text-opacity: 1;
	color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-white {
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
	--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.backdrop-blur {
	--tw-backdrop-blur: blur(8px);
	-webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
		var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
		var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
	backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
		var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
		var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
	-webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
		var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
		var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
	backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
		var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
		var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition-colors {
	transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}

@keyframes enter {
	from {
		opacity: var(--tw-enter-opacity, 1);
		transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0)
			scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1))
			rotate(var(--tw-enter-rotate, 0));
	}
}

@keyframes exit {
	to {
		opacity: var(--tw-exit-opacity, 1);
		transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0)
			scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1))
			rotate(var(--tw-exit-rotate, 0));
	}
}

.file\:mr-4::file-selector-button {
	margin-right: 1rem;
}

.file\:rounded-full::file-selector-button {
	border-radius: 9999px;
}

.file\:border-0::file-selector-button {
	border-width: 0px;
}

.file\:bg-blue-50::file-selector-button {
	--tw-bg-opacity: 1;
	background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.file\:px-4::file-selector-button {
	padding-left: 1rem;
	padding-right: 1rem;
}

.file\:py-2::file-selector-button {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}

.file\:text-sm::file-selector-button {
	font-size: 0.875rem;
	line-height: 1.25rem;
}

.file\:font-semibold::file-selector-button {
	font-weight: 600;
}

.file\:text-blue-700::file-selector-button {
	--tw-text-opacity: 1;
	color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}

.hover\:bg-blue-600:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-700:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-800:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-600:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:text-blue-500:hover {
	--tw-text-opacity: 1;
	color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
	color: hsl(var(--primary));
}

.hover\:file\:bg-blue-100::file-selector-button:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.focus\:border-blue-500:focus {
	--tw-border-opacity: 1;
	border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}

.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}

.focus\:ring-2:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-500:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-offset-2:focus {
	--tw-ring-offset-width: 2px;
}

.disabled\:bg-gray-400:disabled {
	--tw-bg-opacity: 1;
	background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}

@supports ((-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))) {
	.supports-\[backdrop-filter\]\:bg-background\/60 {
		background-color: hsl(var(--background) / 0.6);
	}
}

@media (min-width: 768px) {
	.md\:h-12 {
		height: 3rem;
	}

	.md\:w-12 {
		width: 3rem;
	}
}

@media (min-width: 1024px) {
	.lg\:h-16 {
		height: 4rem;
	}

	.lg\:w-16 {
		width: 4rem;
	}
}
