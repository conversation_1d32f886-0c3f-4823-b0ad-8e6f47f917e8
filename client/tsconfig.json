// CLIENT tsconfig
{
	"extends": "../tsconfig.json",
	"compilerOptions": {
		"lib": ["ESNext", "DOM", "DOM.Iterable"], // Needed for client (browser environment)
		"module": "ESNext",
		"target": "ESNext",
		"types": ["bun-types"],
		"moduleResolution": "bundler",

		"jsx": "react-jsx",
		"jsxImportSource": "react",

		"strict": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"forceConsistentCasingInFileNames": true,

		"noEmit": false, // This config *should* emit JavaScript files for the client bundle
		"allowJs": true,
		"sourceMap": true,
		"strictPropertyInitialization": false,

		// Correctly defines where declaration files are found
		"typeRoots": ["../node_modules/@types", "../node_modules", "./types"],

		"baseUrl": ".", // Base URL for this project (client directory)
		"outDir": "./dist", // Output directory for client compilation
		"paths": {
			"@/*": ["./src/*"],
			"@app-types/*": ["./types/*"],
			"@server-trpc-types": ["../server/trpc/root.ts"]
		}
	},
	"include": ["src/**/*.ts", "src/**/*.tsx", "types/**/*.ts", "config/**/*.ts"],
	"exclude": ["node_modules", "dist", "../server", "../tests"]
}