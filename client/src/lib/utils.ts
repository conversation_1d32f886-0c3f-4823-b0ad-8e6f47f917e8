import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * A utility function to conditionally join class names together.
 * It intelligently merges Tailwind CSS classes without style conflicts.
 * @param inputs - A list of class values to combine.
 * @returns A string of combined class names.
 */
export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}