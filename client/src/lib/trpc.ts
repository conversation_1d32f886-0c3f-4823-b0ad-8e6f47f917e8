import { createTRPC<PERSON>lient, httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import type { AppRouter } from '@server-trpc-types';

// Create the tRPC React hooks
export const trpc = createTRPCReact<AppRouter>();

// Create the tRPC client
export const trpcClient = createTRPCClient<AppRouter>({
	links: [
		httpBatchLink({
			url: '/api/trpc',
			// Add auth headers
			headers: () => {
				const token = localStorage.getItem('accessToken');
				return {
					authorization: token ? `Bearer ${token}` : ''
				};
			}
		})
	]
});

// Vanilla client for use outside of React components
export const trpcVanilla = trpcClient;