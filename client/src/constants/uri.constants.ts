// Client-side URI constants using build-time environment variables
// These are injected at build time by the dev-server/build process

const API_VERSION = import.meta.env?.API_VERSION || 'v1';
const IDP_SERVICE_URL = import.meta.env?.IDP_SERVICE_URL || 'http://localhost:6002';
const CALC_SERVICE_URL = import.meta.env?.CALC_SERVICE_URL || 'http://localhost:6003';
const FINANCE_SERVICE_URL = import.meta.env?.FINANCE_SERVICE_URL || 'http://localhost:6001';

const API_SUFFIX = `api/${API_VERSION}`;

export const UriConstants = {
	IDP_API_URL: `${IDP_SERVICE_URL}/${API_SUFFIX}`,
	CALC_API_URL: `${CALC_SERVICE_URL}/${API_SUFFIX}`,
	FINANCE_API_URL: `${FINANCE_SERVICE_URL}/${API_SUFFIX}`
};