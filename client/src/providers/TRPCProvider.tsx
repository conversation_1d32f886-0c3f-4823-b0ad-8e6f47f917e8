import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import React, { useState } from 'react';
import { trpc } from '@/lib/trpc';

interface TRPCProviderProps {
	children: React.ReactNode;
}

export function TRPCProvider({ children }: TRPCProviderProps) {
	const [queryClient] = useState(
		() =>
			new QueryClient({
				defaultOptions: {
					queries: {
						staleTime: 5 * 60 * 1000, // 5 minutes
						retry: (failureCount, error: any) => {
							// Don't retry on 4xx errors
							if (error?.data?.httpStatus >= 400 && error?.data?.httpStatus < 500) {
								return false;
							}
							return failureCount < 3;
						}
					}
				}
			})
	);

	const [trpcClient] = useState(() =>
		trpc.createClient({
			links: [
				httpBatchLink({
					url: '/api/trpc',
					// Add auth headers
					headers: () => {
						const token = localStorage.getItem('accessToken');
						return {
							authorization: token ? `Bearer ${token}` : '',
							'content-type': 'application/json'
						};
					},
					// Handle errors
					fetch: async (url, options) => {
						const response = await fetch(url, options as RequestInit);

						// Handle 401 Unauthorized
						if (response.status === 401) {
							// Clear stored tokens
							localStorage.removeItem('accessToken');
							localStorage.removeItem('refreshToken');

							// Optionally redirect to login
							if (window.location.pathname !== '/login') {
								window.location.href = '/login';
							}
						}

						return response;
					}
				})
			]
		})
	);

	return (
		<trpc.Provider client={trpcClient} queryClient={queryClient}>
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		</trpc.Provider>
	);
}