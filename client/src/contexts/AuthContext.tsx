import { type ReactNode, createContext, useCallback, useEffect, useState } from 'react';
import type { TRPCRegisterInput, AuthContextType, User } from '@app-types/auth.types';
import type { AccessTokenPayload, LoginDTO } from 'excelytics.shared-dtos';
import { jwtDecode } from 'jwt-decode';
import { trpc } from '@/lib/trpc';

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [token, setToken] = useState<string | null>(null);
	const [user, setUser] = useState<User | null>(null);

	// tRPC mutations
	const loginMutation = trpc.identity.login.useMutation();
	const registerMutation = trpc.identity.register.useMutation();

	// This effect runs once on initial load to check for an existing session
	useEffect(() => {
		try {
			const storedToken = localStorage.getItem('authToken');
			if (storedToken) {
				// Decode the token using your specific AccessTokenPayload type
				const decoded = jwtDecode<AccessTokenPayload>(storedToken);

				// The 'exp' property is standard in JWTs, even if not in your type.
				// jwt-decode will find it. We'll cast to any to access it.
				const expiresAt = (decoded as any).exp * 1000;

				if (expiresAt > Date.now()) {
					setToken(storedToken);
					setUser(decoded);
				} else {
					localStorage.removeItem('authToken');
				}
			}
		} catch (error) {
			console.error('Failed to process stored token:', error);
			localStorage.removeItem('authToken');
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Define logout first
	const logout = useCallback(() => {
		localStorage.removeItem('authToken');
		setToken(null);
		setUser(null);
	}, []);

	const handleAuthSuccess = useCallback(
		(newToken: string) => {
			try {
				const decoded = jwtDecode<AccessTokenPayload>(newToken);
				localStorage.setItem('authToken', newToken);
				setToken(newToken);
				setUser(decoded);
			} catch (error) {
				console.error('Failed to decode new token:', error);
				logout(); // Ensure clean state on failure
			}
		},
		[logout]
	);

	const login = useCallback(
		async (credentials: LoginDTO) => {
			const result = await loginMutation.mutateAsync(credentials);
			if (result.tokens?.accessToken) {
				handleAuthSuccess(result.tokens.accessToken);
			}
		},
		[handleAuthSuccess, loginMutation]
	);

	const register = useCallback(
		async (details: TRPCRegisterInput) => {
			const result = await registerMutation.mutateAsync(details);
			if (result.tokens?.accessToken) {
				handleAuthSuccess(result.tokens.accessToken);
			}
		},
		[handleAuthSuccess, registerMutation]
	);

	const isAuthenticated = !!token;

	return (
		<AuthContext.Provider
			value={{
				isAuthenticated,
				user,
				token,
				login,
				register,
				logout,
				isLoading
			}}
		>
			{children}
		</AuthContext.Provider>
	);
};