import MainLayout from '@/components/layout/MainLayout';
import KwhDashboardPage from '@/pages/KwhDashboardPage';
import ProtectedRoute from '@/routes/ProtectedRoutes';
import RegisterPage from '@/pages/auth/RegisterPage';
import DashboardPage from '@/pages/DashboardPage';
import { Routes, Route } from 'react-router-dom';
import TRPCDemoPage from '@/pages/TRPCDemoPage';
import NotFoundPage from '@/pages/NotFoundPage';
import LoginPage from '@/pages/auth/LoginPage';
import HomePage from '@/pages/HomePage';

const AppRoutes = () => {
	return (
		<Routes>
			<Route element={<MainLayout />}>
				{/* Public Routes */}
				<Route path="/" element={<HomePage />} />
				<Route path="/kwh-dashboard" element={<KwhDashboardPage />} />
				<Route path="/trpc-demo" element={<TRPCDemoPage />} />
				<Route path="/login" element={<LoginPage />} />
				<Route path="/register" element={<RegisterPage />} />

				{/* Protected Routes */}
				<Route element={<ProtectedRoute />}>
					<Route path="/dashboard" element={<DashboardPage />} />
				</Route>

				{/* Catch-all Not Found Route */}
				<Route path="*" element={<NotFoundPage />} />
			</Route>
		</Routes>
	);
};

export default AppRoutes;