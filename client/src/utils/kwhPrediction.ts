import { DailyUsageDataItem, ActualKwhDataItem } from '@app-types/kwh-meter/dashboard.types';
import { PredictionResult, KwhDataPoint } from '@app-types/kwh-meter/kwh.types';

// Calculate daily usage from meter readings
export const calculateDailyUsage = (meterReadings: ActualKwhDataItem[]): DailyUsageDataItem[] => {
	if (meterReadings.length < 2) return [];

	const sortedReadings = [...meterReadings].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
	const dailyUsage: DailyUsageDataItem[] = [];

	for (let i = 1; i < sortedReadings.length; i++) {
		const current = sortedReadings[i];
		const previous = sortedReadings[i - 1];

		// Calculate usage as the difference between consecutive readings
		// Note: Since meter readings are decreasing (prepaid), usage is previous - current
		const usage = Math.abs(previous.kwh - current.kwh);

		dailyUsage.push({
			date: current.date,
			usage: Math.round(usage * 100) / 100, // Round to 2 decimal places
			day: current.day,
			meterReading: current.kwh
		});
	}

	return dailyUsage;
};

// Convert daily usage back to KwhDataPoint format for existing prediction functions
export const convertUsageToKwhDataPoint = (usageData: DailyUsageDataItem[]): KwhDataPoint[] => {
	return usageData.map(item => ({
		date: item.date,
		kwh: item.usage,
		day: item.day
	}));
};

// Simple linear regression
export const calculateLinearRegression = (data: KwhDataPoint[]) => {
	const n = data.length;
	const sumX = data.reduce((sum, _, i) => sum + i, 0);
	const sumY = data.reduce((sum, item) => sum + item.kwh, 0);
	const sumXY = data.reduce((sum, item, i) => sum + i * item.kwh, 0);
	const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);

	const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
	const intercept = (sumY - slope * sumX) / n;

	return { slope, intercept };
};

// Moving average prediction
export const calculateMovingAverage = (data: KwhDataPoint[], window: number = 7) => {
	if (data.length < window) return data[data.length - 1]?.kwh || 0;

	const lastValues = data.slice(-window);
	return lastValues.reduce((sum, item) => sum + item.kwh, 0) / window;
};

// Seasonal adjustment (day of week patterns)
export const calculateSeasonalPattern = (data: KwhDataPoint[]) => {
	const dayPatterns: { [key: string]: number[] } = {};

	data.forEach(item => {
		if (!dayPatterns[item.day]) {
			dayPatterns[item.day] = [];
		}
		dayPatterns[item.day].push(item.kwh);
	});

	const dayAverages: { [key: string]: number } = {};
	Object.keys(dayPatterns).forEach(day => {
		const values = dayPatterns[day];
		dayAverages[day] = values.reduce((sum, val) => sum + val, 0) / values.length;
	});

	return dayAverages;
};

// Enhanced prediction with multiple methods
export const generateAdvancedPredictions = (data: KwhDataPoint[], days: number = 7): PredictionResult[] => {
	if (data.length < 3) return [];

	const predictions: PredictionResult[] = [];
	const lastDate = new Date(data[data.length - 1].date);

	// Determine if this is meter reading data (decreasing) or usage data (varying)
	const isDecreasingTrend = data.length >= 2 && data[0].kwh > data[data.length - 1].kwh;

	if (isDecreasingTrend) {
		// This is meter reading data - predict decreasing trend
		return generateMeterReadingPredictions(data, days, lastDate);
	} else {
		// This is usage data - predict usage patterns
		return generateUsagePredictions(data, days, lastDate);
	}
};

// Predict meter readings (decreasing trend)
const generateMeterReadingPredictions = (data: KwhDataPoint[], days: number, lastDate: Date): PredictionResult[] => {
	const predictions: PredictionResult[] = [];

	// Calculate average daily consumption rate
	const recentData = data.slice(-Math.min(7, data.length));
	let totalConsumption = 0;
	let validDays = 0;

	for (let i = 1; i < recentData.length; i++) {
		const consumption = recentData[i - 1].kwh - recentData[i].kwh;
		if (consumption > 0) {
			totalConsumption += consumption;
			validDays++;
		}
	}

	const avgDailyConsumption = validDays > 0 ? totalConsumption / validDays : 5; // Default 5 kWh/day
	const lastReading = data[data.length - 1].kwh;

	// Add some variance based on day of week
	const dayVariance: { [key: string]: number } = {
		Sun: 0.9,
		Mon: 1.1,
		Tue: 1.0,
		Wed: 1.0,
		Thu: 1.0,
		Fri: 1.2,
		Sat: 1.1
	};

	for (let i = 1; i <= days; i++) {
		const nextDate = new Date(lastDate);
		nextDate.setDate(lastDate.getDate() + i);
		const dayName = nextDate.toLocaleDateString('en-US', { weekday: 'short' });

		const dailyConsumption = avgDailyConsumption * (dayVariance[dayName] || 1.0);
		const predictedReading = Math.max(0, lastReading - dailyConsumption * i);

		// Higher confidence for shorter predictions
		const confidence = Math.max(0.5, 0.95 - i * 0.05);

		predictions.push({
			date: nextDate.toISOString().split('T')[0],
			kwh: 0,
			predicted: Math.round(predictedReading * 10) / 10,
			day: dayName,
			confidence: Math.round(confidence * 100) / 100
		});
	}

	return predictions;
};

// Predict usage patterns (daily consumption)
const generateUsagePredictions = (data: KwhDataPoint[], days: number, lastDate: Date): PredictionResult[] => {
	const predictions: PredictionResult[] = [];

	// Calculate moving average with more weight on recent data
	const recentData = data.slice(-Math.min(7, data.length));
	const weights = recentData.map((_, i) => i + 1); // More weight to recent data
	const totalWeight = weights.reduce((sum, w) => sum + w, 0);

	const weightedAvg =
		recentData.reduce((sum, item, i) => {
			return sum + item.kwh * weights[i];
		}, 0) / totalWeight;

	// Calculate day-of-week patterns if we have enough data
	const dayPatterns = calculateSeasonalPattern(data);
	const overallAvg = data.reduce((sum, item) => sum + item.kwh, 0) / data.length;

	// Calculate trend (increasing/decreasing usage)
	const trendSlope = data.length >= 5 ? calculateTrendSlope(data.slice(-5)) : 0;

	for (let i = 1; i <= days; i++) {
		const nextDate = new Date(lastDate);
		nextDate.setDate(lastDate.getDate() + i);
		const dayName = nextDate.toLocaleDateString('en-US', { weekday: 'short' });

		// Base prediction on weighted average
		let prediction = weightedAvg;

		// Apply seasonal adjustment if we have enough data
		if (dayPatterns[dayName] && Object.keys(dayPatterns).length >= 3) {
			const seasonalMultiplier = dayPatterns[dayName] / overallAvg;
			prediction = prediction * (0.7 + 0.3 * seasonalMultiplier);
		}

		// Apply trend
		prediction += trendSlope * i;

		// Add some randomness but keep it reasonable
		const variance = Math.min(prediction * 0.1, 1); // Max 10% variance or 1 kWh
		prediction += (Math.random() - 0.5) * variance;

		// Ensure positive values
		prediction = Math.max(0.1, prediction);

		// Confidence decreases with distance and increases with data quality
		const dataQuality = Math.min(1, data.length / 10);
		const confidence = Math.max(0.4, 0.9 * dataQuality - i * 0.08);

		predictions.push({
			date: nextDate.toISOString().split('T')[0],
			kwh: 0,
			predicted: Math.round(prediction * 10) / 10,
			day: dayName,
			confidence: Math.round(confidence * 100) / 100
		});
	}

	return predictions;
};

// Calculate trend slope for usage data
const calculateTrendSlope = (data: KwhDataPoint[]): number => {
	if (data.length < 3) return 0;

	const n = data.length;
	const sumX = data.reduce((sum, _, i) => sum + i, 0);
	const sumY = data.reduce((sum, item) => sum + item.kwh, 0);
	const sumXY = data.reduce((sum, item, i) => sum + i * item.kwh, 0);
	const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);

	const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);

	// Limit slope to reasonable values
	return Math.max(-0.5, Math.min(0.5, slope));
};

// Calculate variance for confidence estimation
const calculateVariance = (data: KwhDataPoint[]): number => {
	if (data.length < 2) return 0;

	const mean = data.reduce((sum, item) => sum + item.kwh, 0) / data.length;
	const squaredDiffs = data.map(item => Math.pow(item.kwh - mean, 2));
	return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / data.length;
};

// Detect trends and anomalies
export const analyzeUsagePatterns = (data: KwhDataPoint[]) => {
	if (data.length < 7) return null;

	const recentWeek = data.slice(-7);
	const previousWeek = data.slice(-14, -7);

	if (previousWeek.length < 7) return null;

	const recentAvg = recentWeek.reduce((sum, item) => sum + item.kwh, 0) / 7;
	const previousAvg = previousWeek.reduce((sum, item) => sum + item.kwh, 0) / 7;

	const trendPercentage = ((recentAvg - previousAvg) / previousAvg) * 100;

	// Detect anomalies (values significantly different from recent average)
	const threshold = recentAvg * 0.3; // 30% threshold
	const anomalies = recentWeek.filter(item => Math.abs(item.kwh - recentAvg) > threshold);

	return {
		trend: trendPercentage > 5 ? 'increasing' : trendPercentage < -5 ? 'decreasing' : 'stable',
		trendPercentage: Math.round(trendPercentage * 10) / 10,
		anomalies: anomalies.length,
		recentAverage: Math.round(recentAvg * 10) / 10,
		previousAverage: Math.round(previousAvg * 10) / 10
	};
};

// Generate insights based on usage patterns
export const generateInsights = (data: KwhDataPoint[]): string[] => {
	const insights: string[] = [];

	if (data.length < 7) {
		insights.push('Add more data points for better predictions and insights.');
		return insights;
	}

	const analysis = analyzeUsagePatterns(data);
	if (!analysis) return insights;

	// Trend insights
	if (analysis.trend === 'increasing') {
		insights.push(`Your usage is trending upward by ${analysis.trendPercentage}% compared to last week.`);
	} else if (analysis.trend === 'decreasing') {
		insights.push(
			`Great! Your usage is trending downward by ${Math.abs(analysis.trendPercentage)}% compared to last week.`
		);
	} else {
		insights.push('Your usage has been stable over the past two weeks.');
	}

	// Anomaly insights
	if (analysis.anomalies > 2) {
		insights.push(
			`${analysis.anomalies} days this week had unusual usage patterns. Check for any changes in routine.`
		);
	}

	// Seasonal insights
	const seasonalPatterns = calculateSeasonalPattern(data);
	const dayEntries = Object.entries(seasonalPatterns);
	if (dayEntries.length >= 7) {
		const highest = dayEntries.reduce((max, [day, avg]) => (avg > max.avg ? { day, avg } : max), {
			day: '',
			avg: 0
		});
		const lowest = dayEntries.reduce((min, [day, avg]) => (avg < min.avg ? { day, avg } : min), {
			day: '',
			avg: Infinity
		});

		insights.push(`${highest.day} is typically your highest usage day (${highest.avg.toFixed(1)} kWh average).`);
		insights.push(`${lowest.day} is typically your lowest usage day (${lowest.avg.toFixed(1)} kWh average).`);
	}

	return insights;
};