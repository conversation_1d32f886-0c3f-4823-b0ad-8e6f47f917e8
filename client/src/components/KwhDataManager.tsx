import { <PERSON><PERSON><PERSON><PERSON>, CardContent, Card<PERSON>eader, CardTitle, Card } from '@/components/ui/card';
import { KwhDataManagerProps, KwhDataPoint } from '@app-types/kwh-meter/kwh.types';
import { Download, Trash2, Upload, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import React, { useState } from 'react';

const KwhDataManager: React.FC<KwhDataManagerProps> = ({ data, onDataChange }) => {
	const [newDate, setNewDate] = useState('');
	const [newKwh, setNewKwh] = useState('');

	const addDataPoint = () => {
		if (!newDate || !newKwh) return;

		const date = new Date(newDate);
		const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

		const newPoint: KwhDataPoint = {
			date: newDate,
			kwh: parseFloat(newKwh),
			day: dayName
		};

		const updatedData = [...data, newPoint].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
		onDataChange(updatedData);

		setNewDate('');
		setNewKwh('');
	};

	const removeDataPoint = (index: number) => {
		const updatedData = data.filter((_, i) => i !== index);
		onDataChange(updatedData);
	};

	const exportData = () => {
		const csvContent = ['Date,KWh,Day', ...data.map(item => `${item.date},${item.kwh},${item.day}`)].join('\n');

		const blob = new Blob([csvContent], { type: 'text/csv' });
		const url = window.URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'kwh-data.csv';
		a.click();
		window.URL.revokeObjectURL(url);
	};

	const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.onload = e => {
			const text = e.target?.result as string;
			const lines = text.split('\n');
			const headers = lines[0].split(',');

			if (headers.length < 2) {
				alert('Invalid CSV format. Expected at least Date and KWh columns.');
				return;
			}

			const importedData: KwhDataPoint[] = [];

			for (let i = 1; i < lines.length; i++) {
				const values = lines[i].split(',');
				if (values.length >= 2 && values[0] && values[1]) {
					const date = new Date(values[0]);
					const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

					importedData.push({
						date: values[0],
						kwh: parseFloat(values[1]),
						day: dayName
					});
				}
			}

			if (importedData.length > 0) {
				const sortedData = importedData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
				onDataChange(sortedData);
				alert(`Successfully imported ${importedData.length} data points.`);
			}
		};

		reader.readAsText(file);
		event.target.value = ''; // Reset file input
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Data Management</CardTitle>
				<CardDescription>Add, remove, or import your KWh usage data</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Add New Data Point */}
				<div className="flex gap-2 items-end">
					<div className="flex-1">
						<label className="text-sm font-medium">Date</label>
						<input
							type="date"
							value={newDate}
							onChange={e => setNewDate(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
					<div className="flex-1">
						<label className="text-sm font-medium">KWh Usage</label>
						<input
							type="number"
							step="0.1"
							placeholder="e.g., 15.2"
							value={newKwh}
							onChange={e => setNewKwh(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
					<Button onClick={addDataPoint} disabled={!newDate || !newKwh}>
						<Plus className="w-4 h-4 mr-2" />
						Add
					</Button>
				</div>

				{/* Import/Export Controls */}
				<div className="flex gap-2">
					<Button variant="outline" onClick={exportData} disabled={data.length === 0}>
						<Download className="w-4 h-4 mr-2" />
						Export CSV
					</Button>
					<div className="relative">
						<input
							type="file"
							accept=".csv"
							onChange={importData}
							className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
						/>
						<Button variant="outline">
							<Upload className="w-4 h-4 mr-2" />
							Import CSV
						</Button>
					</div>
				</div>

				{/* Data List */}
				<div className="max-h-60 overflow-y-auto">
					<div className="space-y-2">
						{data.length === 0 ? (
							<p className="text-gray-500 text-center py-4">No data points added yet</p>
						) : (
							data.map((item, index) => (
								<div
									key={index}
									className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
								>
									<div className="flex gap-4">
										<span className="font-medium">{item.date}</span>
										<span className="text-gray-600">({item.day})</span>
										<span className="font-semibold">{item.kwh} kWh</span>
									</div>
									<Button
										variant="outline"
										size="sm"
										onClick={() => removeDataPoint(index)}
										className="text-red-600 hover:text-red-700"
									>
										<Trash2 className="w-4 h-4" />
									</Button>
								</div>
							))
						)}
					</div>
				</div>

				{/* CSV Format Help */}
				<div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
					<strong>CSV Import Format:</strong> Your CSV file should have columns: Date, KWh, Day (optional)
					<br />
					Example: 2025-01-01,15.2,Mon
				</div>
			</CardContent>
		</Card>
	);
};

export default KwhDataManager;