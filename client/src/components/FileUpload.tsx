import { type DragEvent, useState } from 'react';
import { cn } from '@/lib/utils';
// TODO: Implement file upload with tRPC or native fetch for multipart/form-data
// You will need to create these UI components using Shadcn/ui CLI
// import { Button } from '@/components/ui/button';
// import { Progress } from '@/components/ui/progress';

export function FileUpload() {
	const [file, setFile] = useState<File | null>(null);
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);
	const [error, setError] = useState<string | null>(null);
	const [isDragOver, setIsDragOver] = useState(false);

	const handleFileChange = (selectedFile: File | undefined) => {
		if (selectedFile) {
			// You can add file type/size validation here
			setFile(selectedFile);
			setError(null);
		}
	};

	const handleDrop = (e: DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragOver(false);
		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			handleFileChange(e.dataTransfer.files[0]);
		}
	};

	const handleSubmit = async () => {
		if (!file) {
			setError('Please select a file first.');
			return;
		}

		setIsUploading(true);
		setError(null);
		setUploadProgress(0);

		try {
			// TODO: Implement file upload with tRPC or native fetch
			// const result = await uploadExcelFile(file, progress => {
			// 	setUploadProgress(progress);
			// });
			console.log('Upload functionality needs to be implemented with tRPC');
			setError('File upload not yet implemented with tRPC');
		} catch (err) {
			console.error('Upload failed:', err);
			setError('Upload failed. Please try again.');
		} finally {
			setIsUploading(false);
		}
	};

	return (
		<div className="w-full max-w-md mx-auto p-6 border rounded-lg shadow-md">
			<div
				onDragOver={e => {
					e.preventDefault();
					setIsDragOver(true);
				}}
				onDragLeave={() => setIsDragOver(false)}
				onDrop={handleDrop}
				className={cn(
					'border-2 border-dashed rounded-md p-8 text-center cursor-pointer transition-colors',
					isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
				)}
			>
				<input
					type="file"
					id="file-upload"
					className="hidden"
					onChange={e => handleFileChange(e.target.files ? e.target.files[0] : undefined)}
					accept=".xlsx, .xls"
				/>
				<label htmlFor="file-upload" className="cursor-pointer">
					<p>{file ? `Selected: ${file.name}` : 'Drag & drop or click to select an Excel file'}</p>
				</label>
			</div>

			{/* Replace with your actual Shadcn Progress component */}
			{isUploading && (
				<div className="w-full bg-gray-200 rounded-full h-2.5 my-4">
					<div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${uploadProgress}%` }}></div>
				</div>
			)}

			{error && <p className="text-red-500 text-sm mt-2">{error}</p>}

			{/* Replace with your actual Shadcn Button component */}
			<button
				onClick={handleSubmit}
				disabled={!file || isUploading}
				className="mt-4 w-full bg-blue-500 text-white py-2 rounded-md disabled:bg-gray-400"
			>
				{isUploading ? `Uploading... ${uploadProgress}%` : 'Process File'}
			</button>
		</div>
	);
}