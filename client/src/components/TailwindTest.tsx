import React from 'react';

export function TailwindTest() {
	return (
		<div className="p-8 max-w-md mx-auto bg-white rounded-xl shadow-lg space-y-4">
			<div className="text-center">
				<h2 className="text-2xl font-bold text-gray-900">TailwindCSS Test</h2>
				<p className="text-gray-500">If you can see styled content, <PERSON><PERSON><PERSON> is working!</p>
			</div>

			<div className="grid grid-cols-3 gap-4">
				<div className="bg-red-500 h-16 rounded"></div>
				<div className="bg-green-500 h-16 rounded"></div>
				<div className="bg-blue-500 h-16 rounded"></div>
			</div>

			<button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
				Test Button
			</button>

			<div className="flex items-center space-x-2">
				<div className="w-4 h-4 bg-chart-1 rounded-full"></div>
				<span className="text-sm">Chart Color 1</span>
			</div>

			<div className="flex items-center space-x-2">
				<div className="w-4 h-4 bg-chart-2 rounded-full"></div>
				<span className="text-sm">Chart Color 2</span>
			</div>

			<div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
				<p className="text-center text-gray-600">Dashed Border Test</p>
			</div>

			<div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
				<p className="font-semibold">Gradient Background Test</p>
			</div>

			<div className="flex justify-between items-center">
				<span className="text-sm font-medium text-gray-700">Responsive Test:</span>
				<div className="w-8 h-8 bg-primary rounded md:w-12 md:h-12 lg:w-16 lg:h-16"></div>
			</div>

			<div className="space-y-2">
				<div className="text-xs text-gray-400">Typography Scale:</div>
				<div className="text-xs">Extra Small Text</div>
				<div className="text-sm">Small Text</div>
				<div className="text-base">Base Text</div>
				<div className="text-lg">Large Text</div>
				<div className="text-xl">Extra Large Text</div>
			</div>
		</div>
	);
}