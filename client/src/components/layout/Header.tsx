import { useNavigate, <PERSON> } from 'react-router-dom';
// You can replace this with a proper logo component later
import { BarChart3 } from 'lucide-react';
import useAuth from '@/hooks/useAuth';

export default function Header() {
	const { isAuthenticated, logout } = useAuth();
	const navigate = useNavigate();

	const handleLogout = () => {
		logout();
		// Redirect to the homepage after logging out
		navigate('/');
	};

	return (
		<header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="container flex h-14 max-w-screen-2xl items-center">
				<div className="mr-4 flex">
					<Link to="/" className="mr-6 flex items-center space-x-2">
						<BarChart3 className="h-6 w-6 text-blue-600" />
						<span className="font-bold">Excelytics</span>
					</Link>
				</div>

				<div className="flex flex-1 items-center justify-end space-x-2">
					<nav className="flex items-center gap-4">
						{isAuthenticated ? (
							<>
								<Link
									to="/dashboard"
									className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
								>
									Dashboard
								</Link>
								<button
									onClick={handleLogout}
									className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium"
								>
									Logout
								</button>
							</>
						) : (
							<>
								<Link
									to="/login"
									className="text-sm font-medium text-muted-foreground transition-colors hover:text-primary"
								>
									Login
								</Link>
								<Link
									to="/register"
									className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
								>
									Register
								</Link>
							</>
						)}
					</nav>
				</div>
			</div>
		</header>
	);
}