import { useState } from 'react';

export function FileUploadSonnet() {
	const [file, setFile] = useState<File | null>(null);

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const selectedFile = event.target.files?.[0];
		if (selectedFile) {
			setFile(selectedFile);
		}
	};

	return (
		<div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
			<input
				type="file"
				accept=".xlsx,.xls,.csv"
				onChange={handleFileChange}
				className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
			/>
			{file && <p className="mt-2 text-sm text-gray-600">Selected: {file.name}</p>}
		</div>
	);
}