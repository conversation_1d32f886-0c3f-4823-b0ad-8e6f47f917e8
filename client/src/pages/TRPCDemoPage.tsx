import { CheckCircle, Calculator, Activity, Database, Loader2, XCircle, User } from 'lucide-react';
import { CardDescription, CardContent, CardHeader, CardTitle, Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import React, { useState } from 'react';
import { trpc } from '@/lib/trpc';

const TRPCDemoPage: React.FC = () => {
	const [testResults, setTestResults] = useState<Record<string, 'idle' | 'loading' | 'success' | 'error'>>({});

	// Health check queries
	const financeHealth = trpc.finance.healthCheck.useQuery(undefined, { enabled: false });
	const identityHealth = trpc.identity.healthCheck.useQuery(undefined, { enabled: false });
	const calcHealth = trpc.calc.healthCheck.useQuery(undefined, { enabled: false });
	const apiHealth = trpc.healthCheck.useQuery(undefined, { enabled: false });

	// Example mutations
	const createFinancialEntry = trpc.finance.createFinancialEntry.useMutation();
	const performCalculation = trpc.calc.performCalculation.useMutation();
	const registerUser = trpc.identity.register.useMutation();

	const runHealthCheck = async (service: string, queryFn: () => void) => {
		setTestResults(prev => ({ ...prev, [service]: 'loading' }));
		try {
			queryFn();
			// Wait a bit for the query to complete
			setTimeout(() => {
				setTestResults(prev => ({ ...prev, [service]: 'success' }));
			}, 1000);
		} catch (error) {
			setTestResults(prev => ({ ...prev, [service]: 'error' }));
		}
	};

	const runMutationTest = async (service: string, mutationFn: () => Promise<any>) => {
		setTestResults(prev => ({ ...prev, [service]: 'loading' }));
		try {
			await mutationFn();
			setTestResults(prev => ({ ...prev, [service]: 'success' }));
		} catch (error) {
			setTestResults(prev => ({ ...prev, [service]: 'error' }));
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'loading':
				return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
			case 'success':
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case 'error':
				return <XCircle className="h-4 w-4 text-red-500" />;
			default:
				return <Activity className="h-4 w-4 text-gray-400" />;
		}
	};

	return (
		<div className="container mx-auto p-6 space-y-6">
			<div className="text-center">
				<h1 className="text-3xl font-bold tracking-tight">tRPC Demo & Testing</h1>
				<p className="text-muted-foreground">Test the tRPC API endpoints and microservice communication</p>
			</div>

			{/* Health Checks */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Activity className="h-5 w-5" />
						Service Health Checks
					</CardTitle>
					<CardDescription>Test connectivity to all microservices</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-2">
								<Database className="h-4 w-4" />
								<span className="text-sm font-medium">API Gateway</span>
							</div>
							<div className="flex items-center gap-2">
								{getStatusIcon(testResults.api)}
								<Button
									size="sm"
									variant="outline"
									onClick={() => runHealthCheck('api', () => apiHealth.refetch())}
									disabled={testResults.api === 'loading'}
								>
									Test
								</Button>
							</div>
						</div>

						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-2">
								<Database className="h-4 w-4" />
								<span className="text-sm font-medium">Finance Service</span>
							</div>
							<div className="flex items-center gap-2">
								{getStatusIcon(testResults.finance)}
								<Button
									size="sm"
									variant="outline"
									onClick={() => runHealthCheck('finance', () => financeHealth.refetch())}
									disabled={testResults.finance === 'loading'}
								>
									Test
								</Button>
							</div>
						</div>

						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-2">
								<User className="h-4 w-4" />
								<span className="text-sm font-medium">Identity Service</span>
							</div>
							<div className="flex items-center gap-2">
								{getStatusIcon(testResults.identity)}
								<Button
									size="sm"
									variant="outline"
									onClick={() => runHealthCheck('identity', () => identityHealth.refetch())}
									disabled={testResults.identity === 'loading'}
								>
									Test
								</Button>
							</div>
						</div>

						<div className="flex items-center justify-between p-3 border rounded-lg">
							<div className="flex items-center gap-2">
								<Calculator className="h-4 w-4" />
								<span className="text-sm font-medium">Calc Engine</span>
							</div>
							<div className="flex items-center gap-2">
								{getStatusIcon(testResults.calc)}
								<Button
									size="sm"
									variant="outline"
									onClick={() => runHealthCheck('calc', () => calcHealth.refetch())}
									disabled={testResults.calc === 'loading'}
								>
									Test
								</Button>
							</div>
						</div>
					</div>

					{/* Health Check Results */}
					<div className="space-y-2">
						{apiHealth.data && (
							<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
								<pre className="text-xs text-green-800">{JSON.stringify(apiHealth.data, null, 2)}</pre>
							</div>
						)}
						{financeHealth.data && (
							<div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
								<pre className="text-xs text-blue-800">
									{JSON.stringify(financeHealth.data, null, 2)}
								</pre>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Mutation Tests */}
			<Card>
				<CardHeader>
					<CardTitle>API Mutation Tests</CardTitle>
					<CardDescription>Test create, update, and delete operations</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid gap-4 md:grid-cols-3">
						{/* Finance Test */}
						<div className="p-4 border rounded-lg space-y-3">
							<div className="flex items-center gap-2">
								<Database className="h-4 w-4" />
								<span className="font-medium">Finance Entry</span>
								{getStatusIcon(testResults.financeCreate)}
							</div>
							<Button
								className="w-full"
								onClick={() =>
									runMutationTest('financeCreate', () =>
										createFinancialEntry.mutateAsync({
											amount: 1500.0,
											category: 'Test Revenue',
											description: 'tRPC Demo Transaction',
											date: new Date().toISOString().split('T')[0],
											type: 'income'
										})
									)
								}
								disabled={testResults.financeCreate === 'loading'}
							>
								Create Financial Entry
							</Button>
							{createFinancialEntry.data && (
								<div className="p-2 bg-gray-50 rounded text-xs">
									<pre>{JSON.stringify(createFinancialEntry.data, null, 2)}</pre>
								</div>
							)}
						</div>

						{/* Calculation Test */}
						<div className="p-4 border rounded-lg space-y-3">
							<div className="flex items-center gap-2">
								<Calculator className="h-4 w-4" />
								<span className="font-medium">Calculation</span>
								{getStatusIcon(testResults.calculation)}
							</div>
							<Button
								className="w-full"
								onClick={() =>
									runMutationTest('calculation', () =>
										performCalculation.mutateAsync({
											type: 'sum',
											data: [100, 200, 300, 400, 500],
											options: { precision: 2 }
										})
									)
								}
								disabled={testResults.calculation === 'loading'}
							>
								Perform Calculation
							</Button>
							{performCalculation.data && (
								<div className="p-2 bg-gray-50 rounded text-xs">
									<pre>{JSON.stringify(performCalculation.data, null, 2)}</pre>
								</div>
							)}
						</div>

						{/* User Registration Test */}
						<div className="p-4 border rounded-lg space-y-3">
							<div className="flex items-center gap-2">
								<User className="h-4 w-4" />
								<span className="font-medium">User Registration</span>
								{getStatusIcon(testResults.userReg)}
							</div>
							<Button
								className="w-full"
								onClick={() =>
									runMutationTest('userReg', () =>
										registerUser.mutateAsync({
											email: `test${Date.now()}@example.com`,
											password: 'TestPassword123!',
											firstName: 'Test',
											lastName: 'User',
											company: 'tRPC Demo Corp'
										})
									)
								}
								disabled={testResults.userReg === 'loading'}
							>
								Register Test User
							</Button>
							{registerUser.data && (
								<div className="p-2 bg-gray-50 rounded text-xs">
									<pre>{JSON.stringify(registerUser.data, null, 2)}</pre>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Error Display */}
			{(createFinancialEntry.error || performCalculation.error || registerUser.error) && (
				<Card>
					<CardHeader>
						<CardTitle className="text-red-600">Errors</CardTitle>
					</CardHeader>
					<CardContent className="space-y-2">
						{createFinancialEntry.error && (
							<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
								<p className="text-sm text-red-800">
									Finance Error: {createFinancialEntry.error.message}
								</p>
							</div>
						)}
						{performCalculation.error && (
							<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
								<p className="text-sm text-red-800">
									Calculation Error: {performCalculation.error.message}
								</p>
							</div>
						)}
						{registerUser.error && (
							<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
								<p className="text-sm text-red-800">Registration Error: {registerUser.error.message}</p>
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* Instructions */}
			<Card>
				<CardHeader>
					<CardTitle>How to Use</CardTitle>
				</CardHeader>
				<CardContent className="space-y-2 text-sm text-muted-foreground">
					<p>
						1. <strong>Health Checks:</strong> Test connectivity to each microservice
					</p>
					<p>
						2. <strong>Mutations:</strong> Test create operations for each service
					</p>
					<p>
						3. <strong>Results:</strong> View the JSON responses from each service
					</p>
					<p>
						4. <strong>Errors:</strong> Any errors will be displayed in the error section
					</p>
					<p className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
						<strong>Note:</strong> This demo uses mock data since the actual microservices are not yet
						connected. Replace the mock implementations in the tRPC routers with actual service calls.
					</p>
				</CardContent>
			</Card>
		</div>
	);
};

export default TRPCDemoPage;