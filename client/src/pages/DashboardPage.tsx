import { FileUploadSonnet } from '@/components/FileUploadSonnet';
import { TailwindTest } from '@/components/TailwindTest';
import useAuth from '@/hooks/useAuth';

export default function DashboardPage() {
	const { user, logout } = useAuth();

	return (
		<div className="p-8">
			<header className="flex justify-between items-center mb-8">
				<h1 className="text-3xl font-bold">Welcome, {user?.email || 'User'}!</h1>
				<button onClick={logout} className="bg-red-500 text-white px-4 py-2 rounded-md">
					Logout
				</button>
			</header>

			<main>
				<section className="mb-12">
					<h2 className="text-2xl font-semibold mb-4">Upload Your Financial Data</h2>
					<FileUploadSonnet />
				</section>

				<section className="mb-12">
					<h2 className="text-2xl font-semibold mb-4">TailwindCSS Test</h2>
					<TailwindTest />
				</section>

				<section>
					<h2 className="text-2xl font-semibold mb-4">Your Analytics Dashboard</h2>
					<div className="p-8 border rounded-lg bg-gray-50 text-center">
						<p>Your charts and analytics will appear here.</p>
						{/* This is where you'll map over data from the Finance service */}
					</div>
				</section>
			</main>
		</div>
	);
}