import {
	CombinedChartDataItem,
	PredictedKwhDataItem,
	DailyUsageDataItem,
	ActualKwhDataItem,
	ChartDisplayType,
	ChartViewType
} from '@app-types/kwh-meter/dashboard.types';
import {
	generateAdvancedPredictions,
	convertUsageToKwhDataPoint,
	analyzeUsagePatterns,
	calculateDailyUsage,
	generateInsights
} from '@/utils/kwhPrediction';
import { TrendingDown, TrendingUp, BarChart3, Lightbulb, Calendar, Settings, Activity, Gauge, Zap } from 'lucide-react';
import { CartesianGrid, LineChart, AreaChart, Bar<PERSON>hart, XAxis, YAxis, Line, Area, Bar } from 'recharts';
import { CardDescription, CardContent, CardHeader, CardTitle, Card } from '@/components/ui/card';
import { ChartTooltipContent, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { DayOfWeek } from '@app-types/kwh-meter/kwh.constants';
import KwhDataManager from '@/components/KwhDataManager';
import React, { useState, useMemo } from 'react';

// TODO: Add time to the data points and chart
// Also get a package to get the day for a specific date
// find a better way to specify dates
// add a page to add data points and store in db??

// Init R1000 for 225.70kwh
const actualBarlowParkData: ActualKwhDataItem[] = [
	// July readings
	{ date: '2025-07-27', kwh: 225.33, day: DayOfWeek.SHORTFORM.SUNDAY }, // 8:30PM
	{ date: '2025-07-28', kwh: 222.22, day: DayOfWeek.SHORTFORM.MONDAY }, // 8:57PM
	{ date: '2025-07-29', kwh: 219.84, day: DayOfWeek.SHORTFORM.TUESDAY }, // 11:18AM
	{ date: '2025-07-30', kwh: 214.8, day: DayOfWeek.SHORTFORM.WEDNESDAY }, // 11:47AM
	{ date: '2025-07-31', kwh: 209.16, day: DayOfWeek.SHORTFORM.THURSDAY }, // 4:30PM
	// August readings
	{ date: '2025-08-01', kwh: 205.88, day: DayOfWeek.SHORTFORM.FRIDAY }, // 10:38AM
	{ date: '2025-08-02', kwh: 200.49, day: DayOfWeek.SHORTFORM.SATURDAY }, // 10:14AM
	{ date: '2025-08-03', kwh: 194.62, day: DayOfWeek.SHORTFORM.SUNDAY }, // 11:05AM
	{ date: '2025-08-04', kwh: 189.44, day: DayOfWeek.SHORTFORM.MONDAY }, // 9:16AM
	{ date: '2025-08-05', kwh: 183.51, day: DayOfWeek.SHORTFORM.TUESDAY }, // 8:29AM
	{ date: '2025-08-06', kwh: 176.98, day: DayOfWeek.SHORTFORM.WEDNESDAY }, // 1:47PM
	{ date: '2025-08-07', kwh: 173.18, day: DayOfWeek.SHORTFORM.THURSDAY }, // 10:00AM
	{ date: '2025-08-08', kwh: 167.37, day: DayOfWeek.SHORTFORM.FRIDAY }, // 9:03AM
	{ date: '2025-08-09', kwh: 160.87, day: DayOfWeek.SHORTFORM.SATURDAY }, // 9:28AM
	{ date: '2025-08-10', kwh: 155.69, day: DayOfWeek.SHORTFORM.SUNDAY } // 9:54AM
];

const chartConfig = {
	kwh: {
		label: 'Meter Reading (kWh)',
		color: 'hsl(var(--chart-1))'
	},
	usage: {
		label: 'Daily Usage (kWh)',
		color: 'hsl(var(--chart-3))'
	},
	predicted: {
		label: 'Predicted',
		color: 'hsl(var(--chart-2))'
	}
};

const KwhDashboardPage: React.FC = () => {
	const [chartView, setChartView] = useState<ChartViewType>('trend');
	const [displayType, setDisplayType] = useState<ChartDisplayType>('line');
	const [showDataManager, setShowDataManager] = useState(false);
	const [kwhData, setKwhData] = useState<ActualKwhDataItem[]>(actualBarlowParkData);

	// Calculate daily usage from meter readings
	const dailyUsageData: DailyUsageDataItem[] = useMemo(() => calculateDailyUsage(kwhData), [kwhData]);

	// Generate predictions based on the current view
	const predictions: PredictedKwhDataItem[] = useMemo(() => {
		if (chartView === 'usage') {
			// For usage predictions, use the daily usage data
			const usageAsKwhData = convertUsageToKwhDataPoint(dailyUsageData);
			return generateAdvancedPredictions(usageAsKwhData);
		} else {
			// For trend predictions, use the meter readings directly
			return generateAdvancedPredictions(kwhData);
		}
	}, [kwhData, dailyUsageData, chartView]);

	const combinedData: CombinedChartDataItem[] = useMemo(() => {
		const actualAsCombined: CombinedChartDataItem[] = kwhData.map((item, index) => {
			const usageItem = dailyUsageData.find(usage => usage.date === item.date);
			return {
				date: item.date,
				day: item.day,
				kwh: item.kwh,
				usage: usageItem?.usage,
				predicted: undefined,
				confidence: undefined
			};
		});

		const predictionsAsCombined: CombinedChartDataItem[] = predictions.map(item => ({
			date: item.date,
			day: item.day,
			predicted: item.predicted,
			confidence: item.confidence,
			kwh: undefined,
			usage: undefined
		}));

		return [...actualAsCombined, ...predictionsAsCombined];
	}, [kwhData, dailyUsageData, predictions]);

	// Generate insights based on current view
	const insights = useMemo(() => {
		if (chartView === 'usage') {
			const usageAsKwhData = convertUsageToKwhDataPoint(dailyUsageData);
			return generateInsights(usageAsKwhData);
		} else {
			return generateInsights(kwhData);
		}
	}, [kwhData, dailyUsageData, chartView]);

	const usageAnalysis = useMemo(() => {
		if (chartView === 'usage') {
			const usageAsKwhData = convertUsageToKwhDataPoint(dailyUsageData);
			return analyzeUsagePatterns(usageAsKwhData);
		} else {
			return analyzeUsagePatterns(kwhData);
		}
	}, [kwhData, dailyUsageData, chartView]);

	const stats = useMemo(() => {
		if (chartView === 'usage') {
			// Calculate stats based on daily usage
			const totalUsage = dailyUsageData.reduce((sum, item) => sum + item.usage, 0);
			const avgDaily = totalUsage / dailyUsageData.length;
			const lastWeekAvg =
				dailyUsageData.slice(-7).reduce((sum, item) => sum + item.usage, 0) /
				Math.min(7, dailyUsageData.length);
			const trend = lastWeekAvg > avgDaily ? 'up' : 'down';
			const trendPercentage = avgDaily > 0 ? Math.abs(((lastWeekAvg - avgDaily) / avgDaily) * 100) : 0;

			return {
				totalUsage: Math.round(totalUsage * 10) / 10,
				avgDaily: Math.round(avgDaily * 10) / 10,
				lastWeekAvg: Math.round(lastWeekAvg * 10) / 10,
				trend,
				trendPercentage: Math.round(trendPercentage * 10) / 10
			};
		} else {
			// Calculate stats based on meter readings (trend view)
			const currentReading = kwhData[kwhData.length - 1]?.kwh || 0;
			const firstReading = kwhData[0]?.kwh || 0;
			const totalConsumption = Math.abs(firstReading - currentReading);
			const avgDaily = totalConsumption / Math.max(1, kwhData.length - 1);

			// For trend, we look at the rate of change in readings
			const recentReadings = kwhData.slice(-7);
			const olderReadings = kwhData.slice(-14, -7);

			let trend: 'up' | 'down' = 'down';
			let trendPercentage = 0;

			if (recentReadings.length >= 2 && olderReadings.length >= 2) {
				const recentRate =
					Math.abs(recentReadings[0].kwh - recentReadings[recentReadings.length - 1].kwh) /
					recentReadings.length;
				const olderRate =
					Math.abs(olderReadings[0].kwh - olderReadings[olderReadings.length - 1].kwh) / olderReadings.length;
				trend = recentRate > olderRate ? 'up' : 'down';
				trendPercentage = olderRate > 0 ? Math.abs(((recentRate - olderRate) / olderRate) * 100) : 0;
			}

			return {
				totalUsage: Math.round(totalConsumption * 10) / 10,
				avgDaily: Math.round(avgDaily * 10) / 10,
				lastWeekAvg: Math.round(avgDaily * 10) / 10, // Simplified for trend view
				trend,
				trendPercentage: Math.round(trendPercentage * 10) / 10
			};
		}
	}, [kwhData, dailyUsageData, chartView]);

	const renderChart = () => {
		const commonProps = {
			data: combinedData,
			margin: { top: 20, right: 30, left: 60, bottom: 60 }
		};

		// Determine which data key to use based on chart view
		const dataKey = chartView === 'trend' ? 'kwh' : 'usage';
		const colorKey = chartView === 'trend' ? 'kwh' : 'usage';

		// Configure Y-axis based on view type
		const yAxisProps =
			chartView === 'usage'
				? {
						domain: ['dataMin - 0.5', 'dataMax + 0.5'],
						tickFormatter: (value: number) => value.toFixed(1),
						label: { value: 'Daily Usage (kWh)', angle: -90, position: 'insideLeft' }
					}
				: {
						label: { value: 'Meter Reading (kWh)', angle: -90, position: 'insideLeft' }
					};

		const xAxisProps = {
			dataKey: 'day',
			label: { value: 'Day of Week', position: 'insideBottom', offset: -10 }
		};

		switch (displayType) {
			case 'bar':
				return (
					<BarChart {...commonProps}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis {...xAxisProps} />
						<YAxis {...yAxisProps} />
						<ChartTooltip content={<ChartTooltipContent />} />
						<Bar dataKey={dataKey} fill={`var(--color-${colorKey})`} />
						<Bar dataKey="predicted" fill="var(--color-predicted)" />
					</BarChart>
				);
			case 'area':
				return (
					<AreaChart {...commonProps}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis {...xAxisProps} />
						<YAxis {...yAxisProps} />
						<ChartTooltip content={<ChartTooltipContent />} />
						<Area
							type="monotone"
							dataKey={dataKey}
							stroke={`var(--color-${colorKey})`}
							fill={`var(--color-${colorKey})`}
							fillOpacity={0.6}
						/>
						<Area
							type="monotone"
							dataKey="predicted"
							stroke="var(--color-predicted)"
							fill="var(--color-predicted)"
							fillOpacity={0.3}
							strokeDasharray="5 5"
						/>
					</AreaChart>
				);
			default:
				return (
					<LineChart {...commonProps}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis {...xAxisProps} />
						<YAxis {...yAxisProps} />
						<ChartTooltip content={<ChartTooltipContent />} />
						<Line type="monotone" dataKey={dataKey} stroke={`var(--color-${colorKey})`} strokeWidth={2} />
						<Line
							type="monotone"
							dataKey="predicted"
							stroke="var(--color-predicted)"
							strokeWidth={2}
							strokeDasharray="5 5"
						/>
					</LineChart>
				);
		}
	};

	return (
		<div className="container mx-auto p-6 space-y-6">
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">KWh Dashboard - Barlow Park Apt.</h1>
					<p className="text-muted-foreground">
						{chartView === 'trend'
							? 'Monitor your meter readings and consumption trends'
							: 'Track your daily electricity usage and predictions'}
					</p>
				</div>
				<div className="flex gap-2">
					{/* Settings */}
					<button
						onClick={() => setShowDataManager(!showDataManager)}
						className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
							showDataManager
								? 'bg-primary text-primary-foreground'
								: 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
						}`}
					>
						<Settings className="w-4 h-4" />
					</button>
				</div>
			</div>

			{/* Stats Cards */}
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							{chartView === 'trend' ? 'Total Consumption' : 'Total Usage'}
						</CardTitle>
						<Zap className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.totalUsage} kWh</div>
						<p className="text-xs text-muted-foreground">
							{chartView === 'trend'
								? `From ${kwhData.length} meter readings`
								: `Last ${dailyUsageData.length} days`}
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Daily Average</CardTitle>
						<Calendar className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.avgDaily} kWh</div>
						<p className="text-xs text-muted-foreground">
							{chartView === 'trend' ? 'Consumption rate' : 'Per day usage'}
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">
							{chartView === 'trend' ? 'Recent Trend' : 'Last Week Avg'}
						</CardTitle>
						{stats.trend === 'up' ? (
							<TrendingUp className="h-4 w-4 text-red-500" />
						) : (
							<TrendingDown className="h-4 w-4 text-green-500" />
						)}
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.lastWeekAvg} kWh</div>
						<p className={`text-xs ${stats.trend === 'up' ? 'text-red-500' : 'text-green-500'}`}>
							{stats.trend === 'up' ? '+' : '-'}
							{stats.trendPercentage}% {chartView === 'trend' ? 'rate change' : 'from overall avg'}
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Predictions</CardTitle>
						<BarChart3 className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{predictions.length}</div>
						<p className="text-xs text-muted-foreground">Days forecasted</p>
					</CardContent>
				</Card>
			</div>

			{/* Data Manager */}
			{showDataManager && <KwhDataManager data={kwhData} onDataChange={setKwhData} />}

			{/* Insights Card */}
			{insights.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Lightbulb className="h-5 w-5" />
							Usage Insights
						</CardTitle>
						<CardDescription>AI-powered analysis of your electricity usage patterns</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="space-y-2">
							{insights.map((insight, index) => (
								<div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-md">
									<div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
									<p className="text-sm text-gray-700">{insight}</p>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			)}

			{/* Main Chart */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>
								{chartView === 'trend'
									? 'Meter Reading Trend & Predictions'
									: 'Daily KWh Usage & Predictions'}
							</CardTitle>
							<CardDescription>
								{chartView === 'trend'
									? 'Your meter readings over time showing consumption trend with 7-day forecast'
									: 'Your daily electricity consumption with 7-day forecast based on usage patterns'}
							</CardDescription>
						</div>

						{/* Chart Controls */}
						<div className="flex gap-2">
							{/* Chart View Toggle */}
							<div className="flex gap-1 bg-secondary rounded-md p-1">
								<button
									onClick={() => setChartView('trend')}
									className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 ${
										chartView === 'trend'
											? 'bg-primary text-primary-foreground'
											: 'text-secondary-foreground hover:bg-secondary/80'
									}`}
								>
									<Gauge className="w-4 h-4" />
									Trend
								</button>
								<button
									onClick={() => setChartView('usage')}
									className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center gap-2 ${
										chartView === 'usage'
											? 'bg-primary text-primary-foreground'
											: 'text-secondary-foreground hover:bg-secondary/80'
									}`}
								>
									<Activity className="w-4 h-4" />
									Usage
								</button>
							</div>

							{/* Display Type Toggle */}
							<div className="flex gap-1 bg-secondary rounded-md p-1">
								<button
									onClick={() => setDisplayType('line')}
									className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
										displayType === 'line'
											? 'bg-primary text-primary-foreground'
											: 'text-secondary-foreground hover:bg-secondary/80'
									}`}
								>
									Line
								</button>
								<button
									onClick={() => setDisplayType('bar')}
									className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
										displayType === 'bar'
											? 'bg-primary text-primary-foreground'
											: 'text-secondary-foreground hover:bg-secondary/80'
									}`}
								>
									<BarChart3 className="w-4 h-4" />
								</button>
								<button
									onClick={() => setDisplayType('area')}
									className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
										displayType === 'area'
											? 'bg-primary text-primary-foreground'
											: 'text-secondary-foreground hover:bg-secondary/80'
									}`}
								>
									Area
								</button>
							</div>
						</div>
					</div>
				</CardHeader>
				<CardContent>
					<ChartContainer config={chartConfig} className="h-[400px]">
						{renderChart()}
					</ChartContainer>
				</CardContent>
			</Card>

			{/* Data Table */}
			<Card>
				<CardHeader>
					<CardTitle>{chartView === 'trend' ? 'Meter Reading Data' : 'Usage Data'}</CardTitle>
					<CardDescription>
						{chartView === 'trend'
							? 'Detailed breakdown of meter readings and consumption'
							: 'Detailed breakdown of daily consumption'}
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="overflow-x-auto">
						<table className="w-full text-sm">
							<thead>
								<tr className="border-b">
									<th className="text-left p-2">Date</th>
									<th className="text-left p-2">Day</th>
									{chartView === 'trend' && <th className="text-right p-2">Meter Reading (kWh)</th>}
									{chartView === 'usage' && <th className="text-right p-2">Daily Usage (kWh)</th>}
									<th className="text-right p-2">Predicted (kWh)</th>
									<th className="text-right p-2">Confidence</th>
								</tr>
							</thead>
							<tbody>
								{combinedData.map((item, index) => (
									<tr key={index} className="border-b">
										<td className="p-2">{item.date}</td>
										<td className="p-2">{item.day}</td>
										{chartView === 'trend' && <td className="text-right p-2">{item.kwh ?? '-'}</td>}
										{chartView === 'usage' && (
											<td className="text-right p-2">{item.usage ?? '-'}</td>
										)}
										<td className="text-right p-2 text-muted-foreground">
											{item.predicted ?? '-'}
										</td>
										<td className="text-right p-2 text-muted-foreground">
											{typeof item.confidence === 'number'
												? `${Math.round(item.confidence * 100)}%`
												: '-'}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default KwhDashboardPage;