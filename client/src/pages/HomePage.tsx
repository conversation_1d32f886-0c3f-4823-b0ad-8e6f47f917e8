import { TailwindTest } from '@/components/TailwindTest';
import { Link } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';

export default function HomePage() {
	const { isAuthenticated } = useAuth();

	return (
		<div className="min-h-screen bg-gray-50">
			{/* Hero Section */}
			<div className="flex flex-col items-center justify-center py-20 text-center">
				<h1 className="text-4xl font-bold mb-4">Welcome to Excelytics</h1>
				<p className="text-lg text-gray-600 mb-8">Your one-stop solution for financial analytics.</p>
				<div className="space-y-4">
					{isAuthenticated ? (
						<Link
							to="/dashboard"
							className="bg-blue-500 text-white px-6 py-3 rounded-md text-lg hover:bg-blue-600 transition-colors"
						>
							Go to Dashboard
						</Link>
					) : (
						<div className="space-x-4">
							<Link
								to="/login"
								className="bg-blue-500 text-white px-6 py-3 rounded-md text-lg hover:bg-blue-600 transition-colors"
							>
								Login
							</Link>
							<Link
								to="/register"
								className="bg-gray-700 text-white px-6 py-3 rounded-md text-lg hover:bg-gray-800 transition-colors"
							>
								Register
							</Link>
						</div>
					)}

					{/* Public Demo Links */}
					<div className="mt-4 space-y-3">
						<div>
							<Link
								to="/kwh-dashboard"
								className="bg-green-500 text-white px-6 py-3 rounded-md text-lg hover:bg-green-600 transition-colors inline-block"
							>
								View KWh Usage Dashboard
							</Link>
							<p className="text-sm text-gray-500 mt-2">
								No login required - Chart dashboard with predictions
							</p>
						</div>
						<div>
							<Link
								to="/trpc-demo"
								className="bg-purple-500 text-white px-6 py-3 rounded-md text-lg hover:bg-purple-600 transition-colors inline-block"
							>
								tRPC API Demo
							</Link>
							<p className="text-sm text-gray-500 mt-2">Test microservice communication via tRPC</p>
						</div>
					</div>
				</div>
			</div>

			{/* TailwindCSS Test Section */}
			<div className="py-16 px-4">
				<div className="max-w-4xl mx-auto">
					<div className="text-center mb-12">
						<h2 className="text-3xl font-bold text-gray-900 mb-4">TailwindCSS Implementation Test</h2>
						<p className="text-lg text-gray-600">
							This section demonstrates that TailwindCSS is properly configured and working.
						</p>
					</div>

					<div className="flex justify-center">
						<TailwindTest />
					</div>
				</div>
			</div>
		</div>
	);
}