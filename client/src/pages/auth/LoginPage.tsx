import type { LoginDTO } from 'excelytics.shared-dtos';
import { useNavigate, Link } from 'react-router-dom';
import React, { useState } from 'react';
import useAuth from '@/hooks/useAuth';

export default function LoginPage() {
	const [error, setError] = useState<string | null>(null);
	const { login, isLoading } = useAuth();
	const navigate = useNavigate();

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setError(null);

		const formData = new FormData(event.currentTarget);
		// We can safely cast here because the form inputs match the DTO fields
		const credentials = Object.fromEntries(formData) as LoginDTO;

		try {
			await login(credentials);
			// On successful login, redirect to the dashboard
			navigate('/dashboard');
		} catch (err: any) {
			console.error('Login failed:', err);
			// Set a user-friendly error message
			const errorMessage =
				err.response?.data?.message || '<PERSON><PERSON> failed. Please check your credentials and try again.';
			setError(errorMessage);
		}
	};

	return (
		<div className="flex items-center justify-center min-h-screen bg-gray-50">
			<div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
				<h1 className="text-2xl font-bold text-center">Login to Excelytics</h1>

				<form className="space-y-6" onSubmit={handleSubmit}>
					<div>
						<label htmlFor="email" className="block text-sm font-medium text-gray-700">
							Email Address
						</label>
						<input
							id="email"
							name="email"
							type="email"
							autoComplete="email"
							required
							className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
						/>
					</div>

					<div>
						<label htmlFor="password" className="block text-sm font-medium text-gray-700">
							Password
						</label>
						<input
							id="password"
							name="password"
							type="password"
							autoComplete="current-password"
							required
							className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
						/>
					</div>

					{error && <p className="text-sm text-red-600">{error}</p>}

					<div>
						<button
							type="submit"
							disabled={isLoading}
							className="w-full px-4 py-2 font-semibold text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
						>
							{isLoading ? 'Logging in...' : 'Login'}
						</button>
					</div>
				</form>

				<p className="text-sm text-center text-gray-600">
					Don&#39;t have an account?{' '}
					<Link to="/register" className="font-medium text-blue-600 hover:text-blue-500">
						Register here
					</Link>
				</p>
			</div>
		</div>
	);
}