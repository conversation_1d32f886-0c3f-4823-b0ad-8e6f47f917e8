import type { TRPCRegisterInput } from '@app-types/auth.types';
import { useNavigate, Link } from 'react-router-dom';
import useAuth from '@/hooks/useAuth';
import { useState } from 'react';

export default function RegisterPage() {
	const [error, setError] = useState<string | null>(null);
	const { register, isLoading } = useAuth();
	const navigate = useNavigate();

	const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setError(null);

		const formData = new FormData(event.currentTarget);
		const formValues = Object.fromEntries(formData);

		// Construct the registration details for tRPC
		const registrationDetails: TRPCRegisterInput = {
			email: formValues.email as string,
			password: formValues.password as string,
			firstName: formValues.firstName as string,
			lastName: formValues.lastName as string,
			company: (formValues.company as string) || undefined
		};

		try {
			await register(registrationDetails);
			// On successful registration, redirect to the dashboard
			navigate('/dashboard');
		} catch (err: any) {
			console.error('Registration failed:', err);
			const errorMessage = err.response?.data?.message || 'Registration failed. Please try again.';
			setError(errorMessage);
		}
	};

	return (
		<div className="flex items-center justify-center min-h-screen bg-gray-50">
			<div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
				<h1 className="text-2xl font-bold text-center">Create Your Excelytics Account</h1>

				<form className="space-y-6" onSubmit={handleSubmit}>
					<div className="grid grid-cols-2 gap-4">
						<div>
							<label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
								First Name
							</label>
							<input
								id="firstName"
								name="firstName"
								type="text"
								autoComplete="given-name"
								required
								className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
							/>
						</div>
						<div>
							<label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
								Last Name
							</label>
							<input
								id="lastName"
								name="lastName"
								type="text"
								autoComplete="family-name"
								required
								className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
							/>
						</div>
					</div>

					<div>
						<label htmlFor="email" className="block text-sm font-medium text-gray-700">
							Email Address
						</label>
						<input
							id="email"
							name="email"
							type="email"
							autoComplete="email"
							required
							className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
						/>
					</div>

					<div>
						<label htmlFor="company" className="block text-sm font-medium text-gray-700">
							Company (Optional)
						</label>
						<input
							id="company"
							name="company"
							type="text"
							autoComplete="organization"
							className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
						/>
					</div>

					<div>
						<label htmlFor="password" className="block text-sm font-medium text-gray-700">
							Password
						</label>
						<input
							id="password"
							name="password"
							type="password"
							autoComplete="new-password"
							required
							className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
						/>
						<p className="mt-2 text-xs text-gray-500">
							Must be at least 8 characters long and include upper, lower, number, and special characters.
						</p>
					</div>

					{error && <p className="text-sm text-red-600">{error}</p>}

					<div>
						<button
							type="submit"
							disabled={isLoading}
							className="w-full px-4 py-2 font-semibold text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400"
						>
							{isLoading ? 'Creating Account...' : 'Register'}
						</button>
					</div>
				</form>

				<p className="text-sm text-center text-gray-600">
					Already have an account?{' '}
					<Link to="/login" className="font-medium text-blue-600 hover:text-blue-500">
						Login here
					</Link>
				</p>
			</div>
		</div>
	);
}