import { <PERSON>rowser<PERSON>outer as Router } from 'react-router-dom';
import { TRPCProvider } from '@/providers/TRPCProvider';
import { AuthProvider } from '@/contexts/AuthContext';
import ReactDOM from 'react-dom/client';
import React from 'react';
import App from '@/app';
import '@/index.css';

const rootElement = document.getElementById('root');
if (!rootElement) {
	throw new Error("Failed to find the root element with ID 'root'");
}

const root = ReactDOM.createRoot(rootElement);

root.render(
	<React.StrictMode>
		<TRPCProvider>
			<AuthProvider>
				<Router>
					<App />
				</Router>
			</AuthProvider>
		</TRPCProvider>
	</React.StrictMode>
);