// Type definitions for authentication responses
export interface AuthTokens {
	accessToken: string;
	refreshToken: string;
	expiresIn?: number;
}

export interface AuthUser {
	id: string;
	email: string;
	firstName?: string;
	lastName?: string;
	company?: string;
	role?: string;
	emailVerified?: boolean;
	isActive?: boolean;
	clientOrigin?: number;
	roles?: string[];
}

export interface AuthResponse {
	success: boolean;
	user?: AuthUser;
	tokens?: AuthTokens;
	response?: any;
	status: number;
	error?: string;
}

// Store for managing test user tokens
export interface TestUserSession {
	user: AuthUser;
	tokens: AuthTokens;
	isAdmin: boolean;
}