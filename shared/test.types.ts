import { AccessTokenPayload, EnumClientPath } from 'excelytics.shared-dtos';
import mongoose from 'mongoose';

/**
 * Test user interface for creating test users
 * Based on Identity model structure
 */
export interface TestUser {
	clientId: mongoose.Types.ObjectId;
	clientOrigin: number;
	clientPath?: EnumClientPath;
	email: string;
	password: string;
	isActive: boolean;
	roles?: string[];
}

/**
 * Authentication tokens returned from auth operations
 * Uses proper SharedInternals types
 */
export interface AuthTokens {
	accessToken: string;
	refreshToken: string;
	tokenPayload?: AccessTokenPayload;
}

export interface SimpleHealthCheckResult {
	service: string;
	version: string;
	timestamp: Date;
	status: 'healthy' | 'unhealthy' | 'degraded';
	responseTime: number;
	dependencies?: SimpleDependencyHealth[];
}

export interface SimpleDependencyHealth {
	name: string;
	status: 'healthy' | 'unhealthy' | 'degraded';
	responseTime?: number;
	error?: string;
}

export interface SimpleHealthResponse {
	success: boolean;
	message: string;
	data: SimpleHealthCheckResult;
}

/** Health check response.body type */
export interface HealthResponseType {
	success: boolean;
	message: string;
	data: {
		service: string;
		version: string;
		timestamp: string;
		status: string;
		responseTime: number;
		dependencies: {
			name: string;
			status: string;
			error?: string;
			responseTime?: number;
		}[];
	};
}

export interface TestResult {
	name: string;
	passed: number;
	failed: number;
	duration: number;
	errors: string[];
}