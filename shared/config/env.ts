/**
 * Simple environment configuration without external dependencies
 * Reads directly from process.env and provides type-safe access
 */

// Helper function to get environment variable with fallback
function getEnvVar(key: string, fallback?: string): string {
	const value = process.env[key] || Bun.env?.[key];
	if (value === undefined && fallback === undefined) {
		throw new Error(`Environment variable ${key} is required but not set`);
	}
	return value || fallback!;
}

// Helper function to get optional environment variable
function getOptionalEnvVar(key: string, fallback?: string): string | undefined {
	return process.env[key] || Bun.env?.[key] || fallback;
}

// Helper function to get boolean environment variable
function getBooleanEnvVar(key: string, fallback: boolean = false): boolean {
	const value = getOptionalEnvVar(key);
	if (!value) return fallback;
	return value.toLowerCase() === 'true';
}

// Helper function to get number environment variable
function getNumberEnvVar(key: string, fallback?: number): number {
	const value = getOptionalEnvVar(key);
	if (!value) {
		if (fallback === undefined) {
			throw new Error(`Environment variable ${key} is required but not set`);
		}
		return fallback;
	}
	const parsed = parseInt(value, 10);
	if (isNaN(parsed)) {
		throw new Error(`Environment variable ${key} must be a valid number, got: ${value}`);
	}
	return parsed;
}

/**
 * Environment configuration object
 * All environment variables are read once at startup
 */
export const ENV = {
	// Application Environment
	NODE_ENV: getOptionalEnvVar('NODE_ENV', 'development') as 'development' | 'production' | 'test',
	ENV: getOptionalEnvVar('ENV', 'development'),
	
	// Service Configuration
	PORT: getNumberEnvVar('PORT', 4200),
	API_VERSION: getOptionalEnvVar('API_VERSION', 'v1'),
	
	// Service URLs (with guaranteed fallbacks)
	IDP_SERVICE_URL: getOptionalEnvVar('IDP_SERVICE_URL', 'http://localhost:6002')!,
	CALC_SERVICE_URL: getOptionalEnvVar('CALC_SERVICE_URL', 'http://localhost:6003')!,
	FINANCE_SERVICE_URL: getOptionalEnvVar('FINANCE_SERVICE_URL', 'http://localhost:6001')!,
	CLIENT_SERVICE_URL: getOptionalEnvVar('CLIENT_SERVICE_URL', 'http://localhost:4200')!,
	
	// Optional Configuration
	API_GATEWAY_URL: getOptionalEnvVar('API_GATEWAY_URL'),
	VPN: getBooleanEnvVar('VPN', false),
	
	// Development/Testing
	TEST_VERBOSE_LOGGING: getBooleanEnvVar('TEST_VERBOSE_LOGGING', false),
	
	// Computed properties
	get isDevelopment() {
		return this.NODE_ENV === 'development';
	},
	
	get isProduction() {
		return this.NODE_ENV === 'production';
	},
	
	get isTest() {
		return this.NODE_ENV === 'test';
	}
} as const;

/**
 * Type-safe environment configuration
 */
export type EnvConfig = typeof ENV;

/**
 * Validate that all required environment variables are set
 * Call this at application startup
 */
export function validateEnv(): void {
	try {
		// Test access to all required variables
		ENV.PORT;
		ENV.IDP_SERVICE_URL;
		ENV.CALC_SERVICE_URL;
		ENV.FINANCE_SERVICE_URL;
		ENV.CLIENT_SERVICE_URL;
		
		console.log('✅ Environment configuration loaded successfully');
		console.log(`📍 Environment: ${ENV.NODE_ENV}`);
		console.log(`🚀 Port: ${ENV.PORT}`);
		console.log(`🔗 IDP Service: ${ENV.IDP_SERVICE_URL}`);
	} catch (error) {
		console.error('❌ Environment validation failed:', error);
		process.exit(1);
	}
}