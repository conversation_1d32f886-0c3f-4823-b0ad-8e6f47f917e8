export const TRPC_SUFFIX = 'api/trpc';
const API_BASE = 'api/v1'; // Default API version

// Base URLs for different services - these are defaults, actual URLs come from environment
export const BASE_URLS = {
	IDP: 'http://localhost:6002/',
	CALC: 'http://localhost:6003/',
	FINANCE: 'http://localhost:6001/',
	CLIENT: 'http://localhost:4200/'
} as const;

export const API_SUFFIXES = {
	TRPC: '/api/trpc',
	IDP: `${API_BASE}/auth`,
	CALC: `${API_BASE}/calc`,
	FINANCE: `${API_BASE}/finance`
} as const;


export const IDP_URI_CONSTANTS = {
	HEALTH: {
		SHALLOW: `${API_BASE}/health`,
		DEEP: `${API_BASE}/health/all`
	},
	AUTH: {
		REGISTER: `${API_BASE}/auth/register`,
		LOGIN: `${API_BASE}/auth/login`,
		REFRESH_TOKEN: `${API_BASE}/auth/refresh-token`,
		GET_SESSION: `${API_BASE}/auth/session`,
		INTROSPECT_TOKEN: `${API_BASE}/auth/introspect-token`,
		LOGOUT: `${API_BASE}/auth/logout`
	},
	USER: {
		GET: `${API_BASE}/identity/me`,
		UPDATE: `${API_BASE}/identity/me`,
		// regular user is only able to retrieve their own profile
		GET_USER_BY_EMAIL: (email: string) => `${API_BASE}/identity/user/${encodeURIComponent(email)}`
	},
	ADMIN: {
		GET_ALL_USERS: `${API_BASE}/identity/users`,
		GET_USER_BY_EMAIL: (email: string) => `${API_BASE}/identity/user/${encodeURIComponent(email)}`,
		UPDATE_USER_BY_EMAIL: (email: string) => `${API_BASE}/identity/user/${encodeURIComponent(email)}`,
		DELETE_USER_BY_EMAIL: (email: string) => `${API_BASE}/identity/user/${encodeURIComponent(email)}`,
		DELETE_USER_BY_CLIENT_ID: (clientId: string) => `${API_BASE}/identity/clientId/${encodeURIComponent(clientId)}`
	}
} as const;