{
	"compilerOptions": {
		"target": "ES2022",
		"module": "ESNext",
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"strict": true,
		"noEmit": true,
		"skipLibCheck": true,
		"baseUrl": "..", // Base URL for the project root
		"paths": {
			"@/*": ["./*"]
		}
	},
	"include": [
		"**/*.ts"
	],
	"exclude": [
		"node_modules"
	]
}